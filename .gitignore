# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*.local
.env
.env.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Bun
.bun
bun.lockb

# Bot compiled files
bot/dist/
bot/node_modules/
bot/bun.lockb
bot/.env
bot/.env.local

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Test files
test-*
*.test.png
*.test.jpg
