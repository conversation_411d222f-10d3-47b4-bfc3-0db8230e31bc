# Update these with your Supabase details from your project settings > API
# https://app.supabase.com/project/_/settings/api
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-anon-key

# Supabase service key (for bot server-side operations)
SUPABASE_SERVICE_KEY=your-supabase-service-key

# Telegram Bot
TELEGRAM_BOT_TOKEN=your-telegram-bot-token

# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Watermark Configuration (optional)
WATERMARK_ENABLED=true
WATERMARK_POSITION=bottom-right
WATERMARK_OPACITY=0.8
WATERMARK_PADDING=15
WATERMARK_SCALE=0.08
