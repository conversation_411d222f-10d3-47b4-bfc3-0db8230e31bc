import OpenAI from "openai";
import { addWatermarkFromUrl } from "./watermark";

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
});

// Fetch with retry logic and timeout handling
async function fetchWithRetry(url: string, options: RequestInit & { timeout?: number } = {}, maxRetries = 3): Promise<Response> {
  const { timeout = 30000, ...fetchOptions } = options;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      const response = await fetch(url, {
        ...fetchOptions,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        return response;
      }
      
      // If not ok but not the last retry, continue to next attempt
      if (attempt < maxRetries) {
        console.warn(`Fetch attempt ${attempt} failed with status ${response.status}, retrying...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // Exponential backoff
        continue;
      }
      
      return response; // Return failed response on last attempt
    } catch (error) {
      console.error(`Fetch attempt ${attempt} failed:`, error);
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
  
  throw new Error(`Failed to fetch after ${maxRetries} attempts`);
}

export interface ImageGenerationResult {
  success: boolean;
  imageUrl?: string;
  originalUrl?: string;
  error?: string;
}

export async function transformImage(imageUrl: string): Promise<ImageGenerationResult> {
  try {
    // Download the image from Telegram with timeout and retry logic
    const imageResponse = await fetchWithRetry(imageUrl, {
      method: 'GET',
      timeout: 30000, // 30 second timeout
    });
    if (!imageResponse.ok) {
      throw new Error(`Failed to download image: ${imageResponse.statusText}`);
    }
    
    const imageBuffer = await imageResponse.arrayBuffer();
    
    // Convert image to PNG format using Sharp
    const sharp = (await import('sharp')).default;
    const pngBuffer = await sharp(Buffer.from(imageBuffer))
      .png()
      .resize(1024, 1024, { fit: 'inside', withoutEnlargement: true })
      .toBuffer();
    
    // Check if image is under 4MB (OpenAI limit)
    if (pngBuffer.length > 4 * 1024 * 1024) {
      throw new Error("Image is too large (over 4MB). Please use a smaller image.");
    }
    
    const imageFile = new File([pngBuffer], "input.png", { type: "image/png" });

    // Use OpenAI's image variation feature to transform the image
    const response = await openai.images.createVariation({
      image: imageFile,
      n: 1,
      size: "1024x1024",
    }, {
      timeout: 60000, // 60 second timeout for OpenAI API
    });

    const originalImageUrl = response.data?.[0]?.url;

    if (!originalImageUrl) {
      return {
        success: false,
        error: "No image URL returned from OpenAI"
      };
    }

    // Add watermark to the image
    try {
      await addWatermarkFromUrl(originalImageUrl);

      // For now, skip Supabase storage upload and return watermarked image as base64
      // TODO: Configure Supabase storage bucket properly
      console.warn('Supabase storage not configured, returning original image');
      return {
        success: true,
        imageUrl: originalImageUrl,
        originalUrl: originalImageUrl
      };
    } catch (watermarkError) {
      console.error('Watermarking failed:', watermarkError);
      // If watermarking fails, return original image
      return {
        success: true,
        imageUrl: originalImageUrl,
        originalUrl: originalImageUrl
      };
    }
  } catch (error) {
    console.error('OpenAI Image Transformation error:', error);
    
    let errorMessage = "Failed to transform image";
    
    if (error instanceof OpenAI.APIError) {
      errorMessage = `OpenAI API Error: ${error.message}`;
    } else if (error instanceof Error) {
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        errorMessage = "Request timed out. Please try again.";
      } else if (error.message.includes('ETIMEDOUT') || error.message.includes('connect')) {
        errorMessage = "Network connection failed. Please check your internet and try again.";
      } else if (error.message.includes('fetch failed')) {
        errorMessage = "Network error occurred. Please try again later.";
      } else {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}

export async function generateImage(prompt: string): Promise<ImageGenerationResult> {
  try {
    const response = await openai.images.generate({
      model: "dall-e-3", // DALL-E 3 model for high-quality image generation
      prompt: prompt,
      n: 1,
      size: "1024x1024",
      quality: "hd", // Use "hd" for high quality with DALL-E 3
    }, {
      timeout: 60000, // 60 second timeout for OpenAI API
    });

    const originalImageUrl = response.data?.[0]?.url;

    if (!originalImageUrl) {
      return {
        success: false,
        error: "No image URL returned from OpenAI"
      };
    }

    // Add watermark to the image
    try {
      await addWatermarkFromUrl(originalImageUrl);

      // For now, skip Supabase storage upload and return watermarked image as base64
      // TODO: Configure Supabase storage bucket properly
      console.warn('Supabase storage not configured, returning original image');
      return {
        success: true,
        imageUrl: originalImageUrl,
        originalUrl: originalImageUrl
      };
    } catch (watermarkError) {
      console.error('Watermarking failed:', watermarkError);
      // If watermarking fails, return original image
      return {
        success: true,
        imageUrl: originalImageUrl,
        originalUrl: originalImageUrl
      };
    }
  } catch (error) {
    console.error('OpenAI API error:', error);
    
    let errorMessage = "Failed to generate image";
    
    if (error instanceof OpenAI.APIError) {
      errorMessage = `OpenAI API Error: ${error.message}`;
    } else if (error instanceof Error) {
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        errorMessage = "Request timed out. Please try again.";
      } else if (error.message.includes('ETIMEDOUT') || error.message.includes('connect')) {
        errorMessage = "Network connection failed. Please check your internet and try again.";
      } else if (error.message.includes('fetch failed')) {
        errorMessage = "Network error occurred. Please try again later.";
      } else {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}