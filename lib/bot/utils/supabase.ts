import { createClient } from "@supabase/supabase-js";

export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_KEY!
);

export interface UserLimits {
  user_id: number;
  daily_limit: number;
  remaining: number;
  last_reset: string;
}

export interface GlobalSettings {
  id: number;
  daily_limit: number;
  remaining: number;
  last_reset: string;
}

export interface GenerationLog {
  id: string;
  user_id: number;
  prompt: string;
  timestamp: string;
  success: boolean;
  image_url?: string;
  error_message?: string;
}

export interface GenerationAllowedResult {
  allowed: boolean;
  user_remaining: number;
  user_limit: number;
  global_remaining: number;
  global_limit: number;
  reason: 'allowed' | 'user_limit_exceeded' | 'global_limit_exceeded';
}

export async function checkGenerationAllowed(userId: number): Promise<GenerationAllowedResult | null> {
  const { data, error } = await supabase
    .rpc('check_generation_allowed', { user_id_param: userId });

  if (error) {
    console.error('Error checking generation allowed:', error);
    return null;
  }

  return data;
}

export async function decrementBothLimits(userId: number): Promise<boolean> {
  const { data, error } = await supabase
    .rpc('decrement_both_limits', { user_id_param: userId });

  if (error) {
    console.error('Error decrementing both limits:', error);
    return false;
  }

  return data;
}

export async function getUserLimits(userId: number): Promise<UserLimits | null> {
  const { data, error } = await supabase
    .rpc('check_and_reset_user_daily_limit', { user_id_param: userId });

  if (error) {
    console.error('Error getting user limits:', error);
    return null;
  }

  return data;
}

export async function getGlobalSettings(): Promise<GlobalSettings | null> {
  const { data, error } = await supabase
    .rpc('check_and_reset_global_daily_limit');

  if (error) {
    console.error('Error getting global settings:', error);
    return null;
  }

  return data;
}

export async function uploadImageToStorage(
  imageBuffer: Buffer,
  filename: string,
  bucket: string = 'generated-images'
): Promise<string | null> {
  try {
    const { error } = await supabase.storage
      .from(bucket)
      .upload(filename, imageBuffer, {
        contentType: 'image/png',
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Error uploading image to storage:', error);
      return null;
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from(bucket)
      .getPublicUrl(filename);

    return publicUrl;
  } catch (error) {
    console.error('Error uploading image to storage:', error);
    return null;
  }
}

export async function logGeneration(
  userId: number,
  prompt: string,
  success: boolean,
  imageUrl?: string,
  errorMessage?: string
): Promise<boolean> {
  const { error } = await supabase
    .from('cry_generation_logs')
    .insert({
      user_id: userId,
      prompt,
      success,
      image_url: imageUrl,
      error_message: errorMessage
    });

  if (error) {
    console.error('Error logging generation:', error);
    return false;
  }

  return true;
}