# Product Requirements Document (PRD): Telegram Image Generation Bot with Rate Limiting and Admin Panel

## 1. Introduction

### 1.1 Overview
This product is a simple Telegram bot that allows users to generate images based on text prompts using OpenAI's latest image generation model, `gpt-image-1`. The bot is built in TypeScript and integrates rate limiting logic that reads user-specific limits from a Supabase database. An accompanying Next.js web application serves as an admin panel for managing user rate limits, viewing usage logs, and configuring bot settings.

The bot handles incoming prompts via Telegram messages, checks the user's rate limit, generates the image if allowed, and sends the resulting image back to the user. If the limit is exceeded, it responds with an appropriate message. The system ensures secure API usage, handles errors gracefully, and logs interactions for auditing.

### 1.2 Objectives
- Provide an easy-to-use Telegram interface for AI image generation.
- Enforce per-user rate limits to prevent abuse and manage costs (e.g., daily generation limits).
- Offer an admin panel built with Next.js for monitoring and managing users and limits.
- Ensure scalability, security, and maintainability using modern TypeScript practices.
- Integrate with OpenAI's API for high-quality image generation.

### 1.3 Scope
- In Scope: Telegram bot for image generation, Supabase-based rate limiting, Next.js admin panel for CRUD operations on user limits.
- Out of Scope: Advanced features like image editing, multi-user collaboration in the bot, payment integration for premium limits, or deployment automation.

### 1.4 Assumptions
- Users have a Telegram account and interact with the bot via text messages.
- OpenAI API key and Supabase credentials are securely managed via environment variables.
- The bot is deployed on a serverless platform like Vercel (for Next.js API routes) or a Node.js host.
- Rate limits are per-user, e.g., 5 generations per day, reset daily.

### 1.5 Success Metrics
- Bot uptime >99%.
- Average response time <10 seconds per generation.
- User satisfaction: >80% positive feedback via optional bot surveys.
- Admin panel: Ability to update limits in real-time, reflected in bot behavior.

## 2. Features

### 2.1 Bot Features
- **Prompt Handling**: Users send a text message with a prompt (e.g., "/generate A cat in space"). The bot parses the prompt and generates an image.
- **Image Generation**: Uses OpenAI's `gpt-image-1` model to create images. Default settings: quality="medium", size="1024x1024", n=1.
- **Response**: Sends the generated image as a photo in Telegram, with a caption including the prompt.
- **Commands**:
  - /start: Welcome message and instructions.
  - /generate [prompt]: Trigger image generation.
  - /limit: Check remaining daily generations.
  - /help: Usage guide.
- **Error Handling**: Graceful responses for invalid prompts, API errors, or rate limit exceeded.
- **Logging**: Log each generation attempt to Supabase for auditing.

### 2.2 Rate Limiting
- **Mechanism**: Before generating an image, query Supabase for the user's remaining limit (e.g., from a `user_limits` table).
- **Logic**: If remaining >0, proceed and decrement the count. Reset limits daily (e.g., via a cron job or on-first-use check).
- **Configuration**: Limits stored per Telegram user ID (e.g., default 5/day for free users).
- **Fallback**: If DB query fails, default to a safe limit (e.g., 1).

### 2.3 Next.js Admin Panel Features
- **Authentication**: Simple login using Supabase Auth (email/password or OAuth).
- **Dashboard**: View list of users, their current limits, and usage history.
- **CRUD Operations**:
  - Create/Update user limits (e.g., set daily limit to 10 for a specific user).
  - View logs of generations (user ID, prompt, timestamp, success/failure).
- **UI**: Responsive design with tables for data display, forms for updates.
- **API Routes**: Next.js API endpoints to interact with Supabase (e.g., /api/users, /api/logs).

## 3. Technical Requirements

### 3.1 Tech Stack
- **Bot**: TypeScript, Node.js, Grammy (Telegram bot framework).
- **Image Generation**: OpenAI Node.js SDK.
- **Database**: Supabase (Postgres) for rate limits and logs.
- **Admin Panel**: Next.js (React), Tailwind CSS for styling, Supabase JS client.
- **Deployment**: Vercel for Next.js (including API routes); Bot can run as a long-polling service or webhook on Vercel.
- **Environment**: Node.js v20+, TypeScript v5+.

### 3.2 APIs and Integrations
- **Telegram Bot API**: For bot interactions.
- **OpenAI API**: Image generation endpoint (images/generations).
- **Supabase**: Database queries and auth.

### 3.3 Security
- Store API keys in .env files (never commit).
- Use HTTPS for all communications.
- Validate user inputs to prevent injection attacks.
- Supabase Row Level Security (RLS) for admin-only access to certain tables.

### 3.4 Performance
- Cache DB queries if possible (e.g., user limits for short periods).
- Handle concurrent requests (Grammy supports this natively).

## 4. Architecture

### 4.1 High-Level Diagram (Text Description)
- **User** → Telegram → Bot (TypeScript/Grammy) → Check Rate Limit (Supabase Query) → If OK, Generate Image (OpenAI API) → Send Image back to User.
- **Admin** → Next.js App → Auth (Supabase) → API Routes → CRUD on Supabase DB.
- Components: Bot Server (Node.js), Supabase DB, OpenAI API, Next.js Frontend/Backend.

### 4.2 Data Flow
- Bot receives message → Extract user_id and prompt → Query Supabase: SELECT remaining FROM user_limits WHERE user_id = ? AND date = today.
- If remaining >0, call OpenAI → Update DB: decrement remaining, insert log.
- Admin: Fetch data via Supabase client in Next.js API routes.

## 5. User Flows

### 5.1 Bot Interaction Flow
1. User starts bot (/start) → Bot replies with welcome.
2. User sends /generate [prompt] → Bot checks limit → If OK, generates and sends image; else, "Limit exceeded."
3. User checks /limit → Bot queries DB and replies with remaining.

### 5.2 Admin Panel Flow
1. Admin logs in → Dashboard shows user list.
2. Admin selects user → View/edit limit → Save → Updates Supabase.
3. View logs table for all generations.

## 6. Database Schema

Use Supabase to create the following tables:

- **user_limits**:
  | Column        | Type      | Description                  |
  |---------------|-----------|------------------------------|
  | user_id      | bigint   | Telegram user ID (primary key) |
  | daily_limit  | int      | Max generations per day (default 5) |
  | remaining    | int      | Current remaining today |
  | last_reset   | date     | Date of last reset |

- **generation_logs**:
  | Column        | Type      | Description                  |
  |---------------|-----------|------------------------------|
  | id           | uuid     | Auto-generated ID |
  | user_id      | bigint   | Telegram user ID |
  | prompt       | text     | User prompt |
  | timestamp    | timestamp| Time of request |
  | success      | boolean  | True if generated |
  | image_url    | text     | URL of generated image (if success) |

Enable RLS: Public read for logs (view-only), admin write.

For daily reset: Implement logic in bot to check if last_reset < today, then reset remaining to daily_limit.

## 7. Relevant Documentation

- **OpenAI API (gpt-image-1)**: 
  - Guide: https://platform.openai.com/docs/guides/images-vision
  - Model Details: https://platform.openai.com/docs/models/gpt-image-1
  - Pricing: Approximately $0.02-$0.19 per image depending on quality/size.
  - Node.js SDK: https://www.npmjs.com/package/openai

- **Grammy (Telegram Bot Framework)**: https://grammy.dev/ – Easy-to-use TS library for bots.

- **Supabase**:
  - Database Guide: https://supabase.com/docs/guides/database
  - JS Client: https://supabase.com/docs/reference/javascript/installing
  - Auth: https://supabase.com/docs/guides/auth

- **Next.js**: https://nextjs.org/docs – For admin panel.

- **Telegram Bot API**: https://core.telegram.org/bots/api – For advanced features.

## 8. Code Snippets

### 8.1 Bot Setup (index.ts)
```typescript
import { Bot, Context } from "grammy";
import { createClient } from "@supabase/supabase-js";
import OpenAI from "openai";

const bot = new Bot(process.env.TELEGRAM_BOT_TOKEN!);
const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_KEY!);
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

bot.command("start", (ctx) => ctx.reply("Welcome! Use /generate [prompt] to create an image."));

bot.command("generate", async (ctx: Context) => {
  const prompt = ctx.message?.text?.split(" ").slice(1).join(" ");
  if (!prompt) return ctx.reply("Please provide a prompt.");

  const userId = ctx.from?.id;
  if (!userId) return;

  // Check rate limit
  const { data, error } = await supabase
    .from("user_limits")
    .select("remaining, last_reset")
    .eq("user_id", userId)
    .single();

  if (error || !data) return ctx.reply("Error checking limit.");

  const today = new Date().toISOString().split("T")[0];
  if (data.last_reset !== today) {
    // Reset limit
    await supabase.from("user_limits").update({ remaining: 5, last_reset: today }).eq("user_id", userId);
    data.remaining = 5;
  }

  if (data.remaining <= 0) return ctx.reply("Daily limit exceeded. Try tomorrow!");

  // Generate image
  try {
    const response = await openai.images.generate({
      model: "gpt-image-1",
      prompt,
      n: 1,
      size: "1024x1024",
      quality: "medium",
    });
    const imageUrl = response.data[0].url;

    // Send image
    await ctx.replyWithPhoto(imageUrl!, { caption: `Generated: ${prompt}` });

    // Update limit and log
    await supabase.from("user_limits").update({ remaining: data.remaining - 1 }).eq("user_id", userId);
    await supabase.from("generation_logs").insert({ user_id: userId, prompt, success: true, image_url: imageUrl });
  } catch (err) {
    await supabase.from("generation_logs").insert({ user_id: userId, prompt, success: false });
    ctx.reply("Error generating image. Try again.");
  }
});

bot.start();
```

### 8.2 Rate Limit Check Function (utils.ts)
```typescript
// Extracted for reusability
async function checkAndDecrementLimit(userId: number): Promise<boolean> {
  // Logic as above...
  // Return true if generation allowed
}
```

### 8.3 Next.js Admin Panel Example (pages/api/users.ts)
```typescript
import { createClient } from "@supabase/supabase-js";
import { NextApiRequest, NextApiResponse } from "next";

const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_KEY!);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === "GET") {
    const { data } = await supabase.from("user_limits").select("*");
    res.status(200).json(data);
  } else if (req.method === "POST") {
    // Update limit example
    const { user_id, daily_limit } = req.body;
    await supabase.from("user_limits").upsert({ user_id, daily_limit });
    res.status(200).json({ success: true });
  }
}
```

### 8.4 Next.js Dashboard Page (pages/dashboard.tsx)
```tsx
import { useEffect, useState } from "react";

export default function Dashboard() {
  const [users, setUsers] = useState([]);

  useEffect(() => {
    fetch("/api/users")
      .then((res) => res.json())
      .then(setUsers);
  }, []);

  return (
    <table>
      <thead>
        <tr><th>User ID</th><th>Daily Limit</th><th>Remaining</th></tr>
      </thead>
      <tbody>
        {users.map((user: any) => (
          <tr key={user.user_id}>
            <td>{user.user_id}</td>
            <td>{user.daily_limit}</td>
            <td>{user.remaining}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
```

This PRD provides a complete blueprint. Implement step-by-step, testing integrations with sample data. For deployment, ensure webhooks for the bot if using Vercel.