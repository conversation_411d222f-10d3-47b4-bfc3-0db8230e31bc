# Telegram Bot Timeout Fixes

## Problem
The Telegram bot was experiencing timeouts when generating images through OpenAI's DALL-E 3 API, causing failed image generations and poor user experience.

## Root Cause
- Default Vercel function timeout is 10 seconds
- OpenAI DALL-E 3 image generation can take 60+ seconds
- Image transformation (variations) can also take significant time
- No proper timeout configuration was in place

## Solutions Implemented

### 1. Function-Level Timeout Configuration
Added `maxDuration` export to API routes:

**Files Modified:**
- `app/api/webhook/telegram/route.ts`
- `app/api/generate/route.ts`

**Changes:**
```typescript
// Increase timeout for image generation (up to 300 seconds for Pro plan)
export const maxDuration = 300;
```

### 2. Vercel Configuration File
Created `vercel.json` with function-specific timeouts:

```json
{
  "functions": {
    "app/api/webhook/telegram/route.ts": {
      "maxDuration": 300
    },
    "app/api/generate/route.ts": {
      "maxDuration": 300
    }
  },
  "regions": ["iad1"]
}
```

### 3. OpenAI API Timeout Increases
Updated OpenAI client timeouts in `lib/bot/utils/openai.ts`:

**Before:** 60 seconds
**After:** 240 seconds (4 minutes)

**Functions Updated:**
- `generateImage()` - DALL-E 3 text-to-image generation
- `transformImage()` - Image variation/transformation

### 4. Enhanced Logging
Added comprehensive logging to track performance and debug issues:

**New Logging Features:**
- Process start/end timing
- Individual operation timing (download, processing, OpenAI API calls)
- Detailed error logging with duration information
- Success/failure status with total duration

**Example Log Output:**
```
🎨 Starting DALL-E 3 image generation with prompt: "a cat in space..."
✅ DALL-E 3 generation completed in 45000ms
🎉 Image generation completed successfully in 47500ms
```

## Timeout Limits by Vercel Plan

| Plan | Max Duration |
|------|-------------|
| Hobby | 10 seconds |
| Pro | 300 seconds (5 minutes) |
| Enterprise | 900 seconds (15 minutes) |

**Current Configuration:** 300 seconds (suitable for Pro plan)

## Testing Recommendations

1. **Test Image Generation:**
   ```
   /generate a detailed landscape with mountains and sunset
   ```

2. **Test Image Transformation:**
   - Send a photo to the bot
   - Verify transformation completes without timeout

3. **Monitor Logs:**
   - Check Vercel function logs for timing information
   - Look for timeout-related errors
   - Verify operations complete within 300 seconds

## Monitoring

**Key Metrics to Watch:**
- Function execution duration
- OpenAI API response times
- Success/failure rates
- User experience (no timeout errors)

**Log Patterns to Monitor:**
- `✅ DALL-E 3 generation completed in Xms`
- `🎉 Image generation completed successfully in Xms`
- `❌ Image generation failed after Xms`

## Fallback Strategies

If timeouts still occur:

1. **Reduce Image Quality:**
   - Change from "hd" to "standard" quality
   - Reduce image size from 1024x1024 to 512x512

2. **Implement Async Processing:**
   - Return immediate response to user
   - Process image in background
   - Send result when ready

3. **Add Retry Logic:**
   - Implement exponential backoff
   - Retry failed requests automatically

## Files Modified

1. `app/api/webhook/telegram/route.ts` - Added maxDuration export
2. `app/api/generate/route.ts` - Added maxDuration export  
3. `lib/bot/utils/openai.ts` - Increased timeouts and added logging
4. `vercel.json` - Created with function timeout configuration
5. `TIMEOUT_FIXES.md` - This documentation file

## Next Steps

1. Deploy changes to Vercel
2. Test with real image generation requests
3. Monitor logs for performance improvements
4. Adjust timeouts if needed based on actual usage patterns
