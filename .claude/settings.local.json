{"permissions": {"allow": ["Bash(bun add:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "Bash(npx tsx:*)", "Bash(git add:*)", "Bash(git reset:*)", "Bash(ls:*)", "Bash(git commit:*)", "Bash(gh repo create:*)", "Bash(bun run:*)", "Bash(bunx turbo:*)", "Bash(git push:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(grep:*)", "Bash(find:*)", "Bash(npm audit:*)", "Bash(gh pr:*)", "Bash(git fetch:*)", "Bash(git checkout:*)", "Bash(git pull:*)"], "deny": []}}