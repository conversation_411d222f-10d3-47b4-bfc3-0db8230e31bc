#!/usr/bin/env bun

/**
 * Comprehensive test for logo watermarking functionality
 * This test will help diagnose why the Logo.png isn't appearing on photos
 */

import { addWatermark, addWatermarkFromUrl, getWatermarkConfig, isWatermarkEnabled } from '../lib/bot/utils/watermark';
import { getLogoBuffer, createLogo } from '../lib/bot/utils/logo';
import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';

// Test configuration
const TEST_OUTPUT_DIR = './tests/output';
const TEST_IMAGE_URL = 'https://picsum.photos/1024/1024'; // Random test image

async function ensureTestOutputDir() {
  try {
    await fs.mkdir(TEST_OUTPUT_DIR, { recursive: true });
    console.log('✅ Test output directory created/verified');
  } catch (error) {
    console.error('❌ Failed to create test output directory:', error);
    throw error;
  }
}

async function testEnvironmentConfig() {
  console.log('\n🔧 Testing Environment Configuration...');
  
  const config = getWatermarkConfig();
  const enabled = isWatermarkEnabled();
  
  console.log('Watermark enabled:', enabled);
  console.log('Watermark config:', JSON.stringify(config, null, 2));
  
  // Log environment variables
  console.log('Environment variables:');
  console.log('  WATERMARK_ENABLED:', process.env.WATERMARK_ENABLED);
  console.log('  WATERMARK_POSITION:', process.env.WATERMARK_POSITION);
  console.log('  WATERMARK_OPACITY:', process.env.WATERMARK_OPACITY);
  console.log('  WATERMARK_PADDING:', process.env.WATERMARK_PADDING);
  console.log('  WATERMARK_SCALE:', process.env.WATERMARK_SCALE);
  
  return { config, enabled };
}

async function testLogoFileAccess() {
  console.log('\n📁 Testing Logo File Access...');
  
  // Check if Logo.png exists
  const logoPath = path.join(__dirname, '..', 'lib', 'bot', 'assets', 'Logo.png');
  const logoPathLowercase = path.join(__dirname, '..', 'lib', 'bot', 'assets', 'logo.png');
  
  console.log('Checking Logo.png path:', logoPath);
  console.log('Checking logo.png path:', logoPathLowercase);
  
  try {
    const stats = await fs.stat(logoPath);
    console.log('✅ Logo.png exists, size:', stats.size, 'bytes');
  } catch (error) {
    console.log('❌ Logo.png not found:', error);
  }
  
  try {
    const stats = await fs.stat(logoPathLowercase);
    console.log('✅ logo.png exists, size:', stats.size, 'bytes');
  } catch (error) {
    console.log('❌ logo.png not found:', error);
  }
  
  // Test getLogoBuffer function
  try {
    console.log('\n🔍 Testing getLogoBuffer function...');
    const logoBuffer = await getLogoBuffer();
    console.log('✅ getLogoBuffer succeeded, buffer size:', logoBuffer.length, 'bytes');
    
    // Save the logo buffer to test output
    const logoOutputPath = path.join(TEST_OUTPUT_DIR, 'extracted-logo.png');
    await fs.writeFile(logoOutputPath, logoBuffer);
    console.log('✅ Logo saved to:', logoOutputPath);
    
    return logoBuffer;
  } catch (error) {
    console.error('❌ getLogoBuffer failed:', error);
    
    // Try creating logo programmatically
    console.log('\n🔧 Trying to create logo programmatically...');
    try {
      const createdLogo = await createLogo();
      console.log('✅ createLogo succeeded, buffer size:', createdLogo.length, 'bytes');
      
      const createdLogoPath = path.join(TEST_OUTPUT_DIR, 'created-logo.png');
      await fs.writeFile(createdLogoPath, createdLogo);
      console.log('✅ Created logo saved to:', createdLogoPath);
      
      return createdLogo;
    } catch (createError) {
      console.error('❌ createLogo failed:', createError);
      throw createError;
    }
  }
}

async function createTestImage(): Promise<Buffer> {
  console.log('\n🖼️ Creating test image...');
  
  // Create a simple test image with Sharp
  const testImage = await sharp({
    create: {
      width: 1024,
      height: 1024,
      channels: 3,
      background: { r: 100, g: 150, b: 200 }
    }
  })
  .png()
  .toBuffer();
  
  console.log('✅ Test image created, size:', testImage.length, 'bytes');
  return testImage;
}

async function testWatermarkApplication() {
  console.log('\n🎨 Testing Watermark Application...');
  
  // Create test image
  const testImageBuffer = await createTestImage();
  
  // Save original test image
  const originalPath = path.join(TEST_OUTPUT_DIR, 'original-test-image.png');
  await fs.writeFile(originalPath, testImageBuffer);
  console.log('✅ Original test image saved to:', originalPath);
  
  // Test different watermark positions
  const positions = ['bottom-right', 'bottom-left', 'top-right', 'top-left', 'center'] as const;
  
  for (const position of positions) {
    try {
      console.log(`\n🔧 Testing watermark position: ${position}`);
      
      const watermarkedBuffer = await addWatermark(testImageBuffer, {
        position,
        opacity: 0.8,
        padding: 20,
        scale: 0.15
      });
      
      const outputPath = path.join(TEST_OUTPUT_DIR, `watermarked-${position}.png`);
      await fs.writeFile(outputPath, watermarkedBuffer);
      console.log(`✅ Watermarked image (${position}) saved to:`, outputPath);
      
    } catch (error) {
      console.error(`❌ Failed to create watermark for position ${position}:`, error);
    }
  }
}

async function testWatermarkFromUrl() {
  console.log('\n🌐 Testing Watermark from URL...');
  
  try {
    const watermarkedBuffer = await addWatermarkFromUrl(TEST_IMAGE_URL);
    
    const outputPath = path.join(TEST_OUTPUT_DIR, 'watermarked-from-url.png');
    await fs.writeFile(outputPath, watermarkedBuffer);
    console.log('✅ Watermarked image from URL saved to:', outputPath);
    
  } catch (error) {
    console.error('❌ Failed to watermark image from URL:', error);
  }
}

async function runDiagnostics() {
  console.log('🚀 Starting Logo Watermark Diagnostics...\n');
  
  try {
    // Ensure output directory exists
    await ensureTestOutputDir();
    
    // Test environment configuration
    await testEnvironmentConfig();
    
    // Test logo file access
    await testLogoFileAccess();
    
    // Test watermark application
    await testWatermarkApplication();
    
    // Test watermark from URL
    await testWatermarkFromUrl();
    
    console.log('\n🎉 All tests completed! Check the output directory for results.');
    console.log('📁 Test output directory:', TEST_OUTPUT_DIR);
    
  } catch (error) {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  }
}

// Run diagnostics if this file is executed directly
if (import.meta) runDiagnostics();

export {
  runDiagnostics,
  testEnvironmentConfig,
  testLogoFileAccess,
  testWatermarkApplication,
  testWatermarkFromUrl
};
