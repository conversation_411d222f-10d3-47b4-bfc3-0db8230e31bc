#!/usr/bin/env bun

/**
 * Test script to verify the complete bot image generation pipeline
 * This simulates the actual bot workflow for generating and watermarking images
 */

import { generateImage } from '../lib/bot/utils/openai';
import { addWatermarkFromUrl } from '../lib/bot/utils/watermark';
import fs from 'fs/promises';
import path from 'path';

// Test configuration
const TEST_OUTPUT_DIR = './tests/output';
const TEST_PROMPT = 'A beautiful sunset over mountains with a lake reflection';

async function ensureTestOutputDir() {
  try {
    await fs.mkdir(TEST_OUTPUT_DIR, { recursive: true });
    console.log('✅ Test output directory created/verified');
  } catch (error) {
    console.error('❌ Failed to create test output directory:', error);
    throw error;
  }
}

async function testBotImageGeneration() {
  console.log('🤖 Testing Bot Image Generation Pipeline...\n');
  
  try {
    // Ensure output directory exists
    await ensureTestOutputDir();
    
    console.log('🎨 Generating image with prompt:', TEST_PROMPT);
    console.log('⏳ This may take a moment...\n');
    
    // Test the complete image generation pipeline
    const result = await generateImage(TEST_PROMPT);
    
    if (!result.success) {
      console.error('❌ Image generation failed:', result.error);
      return;
    }
    
    console.log('✅ Image generation successful!');
    console.log('🔗 Generated image URL:', result.imageUrl);
    console.log('🔗 Original image URL:', result.originalUrl);
    
    // Download and save the final watermarked image
    if (result.imageUrl) {
      try {
        console.log('\n⬇️ Downloading final watermarked image...');
        const response = await fetch(result.imageUrl);
        
        if (!response.ok) {
          throw new Error(`Failed to download: ${response.statusText}`);
        }
        
        const imageBuffer = Buffer.from(await response.arrayBuffer());
        const outputPath = path.join(TEST_OUTPUT_DIR, 'bot-generated-watermarked.png');
        
        await fs.writeFile(outputPath, imageBuffer);
        console.log('✅ Final watermarked image saved to:', outputPath);
        console.log('📏 Image size:', imageBuffer.length, 'bytes');
        
      } catch (downloadError) {
        console.error('❌ Failed to download final image:', downloadError);
      }
    }
    
    // Also download the original image for comparison
    if (result.originalUrl && result.originalUrl !== result.imageUrl) {
      try {
        console.log('\n⬇️ Downloading original image for comparison...');
        const response = await fetch(result.originalUrl);
        
        if (!response.ok) {
          throw new Error(`Failed to download original: ${response.statusText}`);
        }
        
        const originalBuffer = Buffer.from(await response.arrayBuffer());
        const originalPath = path.join(TEST_OUTPUT_DIR, 'bot-generated-original.png');
        
        await fs.writeFile(originalPath, originalBuffer);
        console.log('✅ Original image saved to:', originalPath);
        console.log('📏 Original size:', originalBuffer.length, 'bytes');
        
      } catch (downloadError) {
        console.error('❌ Failed to download original image:', downloadError);
      }
    }
    
    console.log('\n🎉 Bot image generation test completed successfully!');
    console.log('📁 Check the output directory for results:', TEST_OUTPUT_DIR);
    
  } catch (error) {
    console.error('\n💥 Bot image generation test failed:', error);
    
    // If OpenAI generation fails, test just the watermarking with a sample image
    console.log('\n🔄 Falling back to watermark-only test...');
    await testWatermarkOnly();
  }
}

async function testWatermarkOnly() {
  console.log('🎨 Testing watermark functionality with sample image...');
  
  try {
    // Use a sample image URL
    const sampleImageUrl = 'https://picsum.photos/1024/1024';
    
    console.log('⬇️ Downloading and watermarking sample image...');
    const watermarkedBuffer = await addWatermarkFromUrl(sampleImageUrl);
    
    const outputPath = path.join(TEST_OUTPUT_DIR, 'watermark-only-test.png');
    await fs.writeFile(outputPath, watermarkedBuffer);
    
    console.log('✅ Watermark-only test completed!');
    console.log('💾 Watermarked sample saved to:', outputPath);
    console.log('📏 Image size:', watermarkedBuffer.length, 'bytes');
    
  } catch (error) {
    console.error('❌ Watermark-only test failed:', error);
  }
}

async function checkEnvironmentSetup() {
  console.log('🔧 Checking Environment Setup...\n');
  
  const requiredEnvVars = [
    'OPENAI_API_KEY',
    'SUPABASE_SERVICE_KEY',
    'NEXT_PUBLIC_SUPABASE_URL'
  ];
  
  const missingVars = [];
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      missingVars.push(envVar);
    } else {
      console.log(`✅ ${envVar}: Set`);
    }
  }
  
  if (missingVars.length > 0) {
    console.log('\n⚠️ Missing environment variables:');
    missingVars.forEach(varName => {
      console.log(`❌ ${varName}: Not set`);
    });
    console.log('\n💡 Make sure to set these in your .env.local file');
    return false;
  }
  
  console.log('\n✅ All required environment variables are set!');
  return true;
}

async function runBotTest() {
  console.log('🚀 Starting Bot Image Generation Test...\n');
  
  // Check environment setup first
  const envOk = await checkEnvironmentSetup();
  
  if (!envOk) {
    console.log('\n⚠️ Environment setup incomplete. Some tests may fail.');
  }
  
  // Run the main test
  await testBotImageGeneration();
}

// Run the test if this file is executed directly
if (import.meta) runBotTest();

export {
  runBotTest,
  testBotImageGeneration,
  testWatermarkOnly,
  checkEnvironmentSetup
};
