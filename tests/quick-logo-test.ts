#!/usr/bin/env bun

/**
 * Quick test to verify the Logo.png file is being used correctly
 */

import { getLogoBuffer } from '../lib/bot/utils/logo';
import { addWatermark } from '../lib/bot/utils/watermark';
import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';

const TEST_OUTPUT_DIR = './tests/output';

async function quickLogoTest() {
  console.log('🚀 Quick Logo Test...\n');
  
  try {
    // Ensure output directory exists
    await fs.mkdir(TEST_OUTPUT_DIR, { recursive: true });
    
    // Test 1: Load the logo
    console.log('📁 Testing logo loading...');
    const logoBuffer = await getLogoBuffer();
    console.log('✅ Logo loaded successfully, size:', logoBuffer.length, 'bytes');
    
    // Save the logo for inspection
    const logoPath = path.join(TEST_OUTPUT_DIR, 'quick-test-logo.png');
    await fs.writeFile(logoPath, logoBuffer);
    console.log('💾 Logo saved to:', logoPath);
    
    // Test 2: Create a simple test image
    console.log('\n🖼️ Creating test image...');
    const testImage = await sharp({
      create: {
        width: 800,
        height: 600,
        channels: 3,
        background: { r: 255, g: 255, b: 255 } // White background
      }
    })
    .png()
    .toBuffer();
    
    // Test 3: Apply watermark
    console.log('🎨 Applying watermark...');
    const watermarkedImage = await addWatermark(testImage, {
      position: 'bottom-right',
      padding: 20,
      scale: 0.15
    });
    
    // Save watermarked image
    const watermarkedPath = path.join(TEST_OUTPUT_DIR, 'quick-test-watermarked.png');
    await fs.writeFile(watermarkedPath, watermarkedImage);
    console.log('✅ Watermarked image saved to:', watermarkedPath);
    
    console.log('\n🎉 Quick logo test completed successfully!');
    console.log('📁 Check these files:');
    console.log('  - Logo:', logoPath);
    console.log('  - Watermarked:', watermarkedPath);
    
  } catch (error) {
    console.error('❌ Quick logo test failed:', error);
  }
}

// Run if executed directly
if (import.meta) quickLogoTest();

export { quickLogoTest };
