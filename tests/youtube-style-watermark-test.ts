#!/usr/bin/env bun

/**
 * Test to verify the new YouTube-style watermark sizing and positioning
 */

import { addWatermark, getWatermarkConfig } from '../lib/bot/utils/watermark';
import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';

const TEST_OUTPUT_DIR = './tests/output';

async function createTestImages() {
  console.log('🎬 Testing YouTube-Style Watermark...\n');
  
  try {
    // Ensure output directory exists
    await fs.mkdir(TEST_OUTPUT_DIR, { recursive: true });
    
    // Get current watermark config
    const config = getWatermarkConfig();
    console.log('⚙️ Current watermark config:', JSON.stringify(config, null, 2));
    
    // Test with different image sizes (like YouTube videos)
    const testSizes = [
      { name: '1080p', width: 1920, height: 1080 },
      { name: '720p', width: 1280, height: 720 },
      { name: 'Square', width: 1024, height: 1024 },
      { name: '4K', width: 3840, height: 2160 }
    ];
    
    for (const size of testSizes) {
      console.log(`\n📐 Testing ${size.name} (${size.width}x${size.height})...`);
      
      // Create test image with gradient background
      const testImage = await sharp({
        create: {
          width: size.width,
          height: size.height,
          channels: 3,
          background: { r: 50, g: 100, b: 200 }
        }
      })
      .composite([
        {
          input: await sharp({
            create: {
              width: size.width,
              height: size.height,
              channels: 4,
              background: { r: 255, g: 255, b: 255, alpha: 0 }
            }
          })
          .composite([
            {
              input: Buffer.from(`
                <svg width="${size.width}" height="${size.height}">
                  <defs>
                    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:0.3" />
                      <stop offset="50%" style="stop-color:#4ecdc4;stop-opacity:0.3" />
                      <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:0.3" />
                    </linearGradient>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#grad)" />
                  <text x="50%" y="50%" text-anchor="middle" font-family="Arial" font-size="${Math.min(size.width, size.height) * 0.05}" fill="white" opacity="0.7">
                    ${size.name} Test Image
                  </text>
                </svg>
              `),
              top: 0,
              left: 0
            }
          ])
          .png()
          .toBuffer(),
          top: 0,
          left: 0
        }
      ])
      .png()
      .toBuffer();
      
      // Apply YouTube-style watermark
      const watermarkedImage = await addWatermark(testImage, {
        position: 'bottom-right',
        padding: 15,
        scale: 0.08,
        opacity: 0.8
      });
      
      // Save the result
      const outputPath = path.join(TEST_OUTPUT_DIR, `youtube-style-${size.name.toLowerCase()}.png`);
      await fs.writeFile(outputPath, watermarkedImage);
      
      // Calculate actual logo dimensions for this image
      const logoWidth = Math.floor(size.width * 0.08);
      const logoHeight = Math.floor(logoWidth * 0.3);
      
      console.log(`✅ ${size.name} watermarked image saved to: ${outputPath}`);
      console.log(`📏 Logo size: ${logoWidth}x${logoHeight}px (${((logoWidth * logoHeight) / (size.width * size.height) * 100).toFixed(2)}% of image area)`);
    }
    
    // Create a comparison image showing before/after
    console.log('\n🔄 Creating before/after comparison...');
    
    const comparisonWidth = 1024;
    const comparisonHeight = 1024;
    
    // Original image
    const originalImage = await sharp({
      create: {
        width: comparisonWidth,
        height: comparisonHeight,
        channels: 3,
        background: { r: 100, g: 150, b: 200 }
      }
    })
    .composite([
      {
        input: Buffer.from(`
          <svg width="${comparisonWidth}" height="${comparisonHeight}">
            <text x="50%" y="50%" text-anchor="middle" font-family="Arial" font-size="48" fill="white" font-weight="bold">
              ORIGINAL
            </text>
            <text x="50%" y="60%" text-anchor="middle" font-family="Arial" font-size="24" fill="white" opacity="0.8">
              No Watermark
            </text>
          </svg>
        `),
        top: 0,
        left: 0
      }
    ])
    .png()
    .toBuffer();
    
    // Watermarked image
    const watermarkedComparison = await addWatermark(originalImage, {
      position: 'bottom-right',
      padding: 15,
      scale: 0.08,
      opacity: 0.8
    });
    
    // Save comparison images
    const originalPath = path.join(TEST_OUTPUT_DIR, 'comparison-original.png');
    const watermarkedPath = path.join(TEST_OUTPUT_DIR, 'comparison-watermarked.png');
    
    await fs.writeFile(originalPath, originalImage);
    await fs.writeFile(watermarkedPath, watermarkedComparison);
    
    console.log('✅ Comparison images saved:');
    console.log(`  Original: ${originalPath}`);
    console.log(`  Watermarked: ${watermarkedPath}`);
    
    console.log('\n🎉 YouTube-style watermark test completed!');
    console.log('📁 Check the output directory for all test images');
    console.log('\n💡 The logo is now 8% of image width (YouTube-style) with 15px padding');
    
  } catch (error) {
    console.error('❌ YouTube-style watermark test failed:', error);
  }
}

// Run if executed directly
if (import.meta) createTestImages();

export { createTestImages };
