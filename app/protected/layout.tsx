import { EnvVarWarning } from "@/components/env-var-warning";
import { AuthButton } from "@/components/auth-button";
import { ThemeSwitcher } from "@/components/theme-switcher";
import { hasEnvVars } from "@/lib/utils";
import { Sparkles, Home } from "lucide-react";
import Link from "next/link";

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <main className="min-h-screen flex flex-col items-center bg-[#004643]">
      <div className="flex-1 w-full flex flex-col gap-20 items-center">
        <nav className="w-full flex justify-center border-b border-[#abd1c6]/20 h-16 bg-[#004643]">
          <div className="w-full max-w-5xl flex justify-between items-center p-3 px-5 text-sm">
            <div className="flex gap-5 items-center font-semibold">
              <Link href={"/"} className="text-[#fffffe] hover:text-[#f9bc60] transition-colors">
                CryBaby AI
              </Link>
              <div className="flex items-center gap-4 ml-4">
                <Link
                  href={"/protected"}
                  className="flex items-center gap-2 text-[#abd1c6] hover:text-[#fffffe] transition-colors"
                >
                  <Home className="h-4 w-4" />
                  Dashboard
                </Link>
                <Link
                  href={"/generate"}
                  className="flex items-center gap-2 text-[#abd1c6] hover:text-[#fffffe] transition-colors"
                >
                  <Sparkles className="h-4 w-4" />
                  Generate
                </Link>
              </div>
            </div>
            {!hasEnvVars ? <EnvVarWarning /> : <AuthButton />}
          </div>
        </nav>
        <div className="flex-1 flex flex-col gap-20 max-w-5xl p-5">
          {children}
        </div>

        <footer className="w-full flex items-center justify-center border-t border-[#abd1c6]/20 mx-auto text-center text-xs gap-8 py-16">
          <p className="text-[#abd1c6]">
            Powered by{" "}
            <a
              href="https://supabase.com/?utm_source=create-next-app&utm_medium=template&utm_term=nextjs"
              target="_blank"
              className="font-bold hover:underline text-[#f9bc60] hover:text-[#fffffe]"
              rel="noreferrer"
            >
              Supabase
            </a>
          </p>
          <ThemeSwitcher />
        </footer>
      </div>
    </main>
  );
}
