import { redirect } from "next/navigation";

import { createClient } from "@/lib/supabase/server";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Bo<PERSON>, Users, BarChart3 } from "lucide-react";
import Link from "next/link";

export default async function ProtectedPage() {
  const supabase = await createClient();

  const { data, error } = await supabase.auth.getClaims();
  if (error || !data?.claims) {
    redirect("/auth/login");
  }

  return (
    <div className="flex-1 w-full flex flex-col gap-12">
      <div className="flex flex-col gap-8 items-center max-w-4xl mx-auto px-4">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4 text-[#fffffe]">Welcome to CryBaby AI</h1>
          <p className="text-[#abd1c6] mb-8">
            Create stunning retro cartoon images with AI. Manage your bot, monitor activity, and generate amazing artwork.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
          <Card className="bg-[#004643] border-[#abd1c6]/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-[#fffffe]">Image Generator</CardTitle>
              <Bot className="h-4 w-4 text-[#f9bc60]" />
            </CardHeader>
            <CardContent>
              <p className="text-xs text-[#abd1c6] mb-4">
                Create stunning retro cartoon images with AI
              </p>
              <Link href="/generate">
                <Button className="w-full bg-[#f9bc60] hover:bg-[#f9bc60]/90 text-[#001e1d]">Start Creating</Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="bg-[#004643] border-[#abd1c6]/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-[#fffffe]">Dashboard</CardTitle>
              <BarChart3 className="h-4 w-4 text-[#f9bc60]" />
            </CardHeader>
            <CardContent>
              <p className="text-xs text-[#abd1c6] mb-4">
                View analytics, usage statistics, and system overview
              </p>
              <Link href="/admin/dashboard">
                <Button className="w-full bg-[#abd1c6] hover:bg-[#abd1c6]/90 text-[#001e1d]">Open Dashboard</Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="bg-[#004643] border-[#abd1c6]/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-[#fffffe]">User Management</CardTitle>
              <Users className="h-4 w-4 text-[#f9bc60]" />
            </CardHeader>
            <CardContent>
              <p className="text-xs text-[#abd1c6] mb-4">
                Manage user limits, view user activity, and configure permissions
              </p>
              <Link href="/admin/users">
                <Button className="w-full border-[#abd1c6]/30 text-[#abd1c6] hover:bg-[#abd1c6]/10 hover:text-[#fffffe]" variant="outline">Manage Users</Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        <Card className="w-full bg-[#004643] border-[#abd1c6]/20">
          <CardHeader>
            <CardTitle className="text-[#fffffe]">Quick Start Guide</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2 text-[#fffffe]">🎨 Create Your First Image</h3>
              <p className="text-sm text-[#abd1c6]">
                Click &quot;Start Creating&quot; above to access the image generator. You can either:
              </p>
              <ul className="text-sm text-[#abd1c6] mt-2 ml-4 list-disc">
                <li>Enter a text description to generate from scratch</li>
                <li>Upload a photo to transform into CryBaby style</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2 text-[#fffffe]">⚡ Daily Limits</h3>
              <p className="text-sm text-[#abd1c6]">
                Each user gets 10 free generations per day. Global limit applies to all users.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2 text-[#fffffe]">🤖 Telegram Bot</h3>
              <p className="text-sm text-[#abd1c6]">
                The same AI is available via Telegram bot with identical features and limits.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
