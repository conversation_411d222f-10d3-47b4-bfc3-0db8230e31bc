import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

interface GenerationHistoryItem {
  id: string;
  prompt: string;
  imageUrl: string;
  timestamp: string;
  success: boolean;
}

// Get or create numeric user ID from database
async function getNumericUserId(supabase: Awaited<ReturnType<typeof createClient>>, userUuid: string): Promise<number | null> {
  try {
    const { data, error } = await supabase
      .rpc('get_or_create_numeric_user_id', { supabase_uuid: userUuid });
    
    if (error) {
      console.error('Error getting numeric user ID:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in getNumericUserId:', error);
    return null;
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get numeric user ID for database lookup
    const numericUserId = await getNumericUserId(supabase, user.id);
    if (!numericUserId) {
      return NextResponse.json(
        { success: false, error: "Error processing user ID" },
        { status: 500 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    // Fetch generation history from database
    const { data: historyData, error: historyError } = await supabase
      .from('cry_generation_logs')
      .select('id, prompt, timestamp, success, image_url')
      .eq('user_id', numericUserId)
      .order('timestamp', { ascending: false })
      .range(offset, offset + limit - 1);

    if (historyError) {
      console.error('Error fetching history:', historyError);
      return NextResponse.json(
        { success: false, error: "Error fetching generation history" },
        { status: 500 }
      );
    }

    // Transform data to match frontend interface
    const history: GenerationHistoryItem[] = (historyData || []).map(item => ({
      id: item.id,
      prompt: item.prompt,
      imageUrl: item.image_url || '',
      timestamp: item.timestamp,
      success: item.success
    }));

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('cry_generation_logs')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', numericUserId);

    if (countError) {
      console.error('Error getting history count:', countError);
    }

    return NextResponse.json({
      success: true,
      history,
      total: count || 0,
      offset,
      limit
    });

  } catch (error) {
    console.error("🚨 History API error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}