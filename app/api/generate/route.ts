import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { generateImage } from "@/lib/bot/utils/openai";
import { checkGenerationAllowed, decrementBothLimits, logGeneration } from "@/lib/bot/utils/supabase";

// Increase timeout for image generation (up to 300 seconds for Pro plan)
export const maxDuration = 300;

console.log("🔧 Initializing web image generation API endpoint...");

// File upload validation constants
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_MIME_TYPES = [
  'image/jpeg',
  'image/png',
  'image/webp',
  'image/gif'
];

// Validate uploaded file
function validateFile(file: File): { valid: boolean; error?: string } {
  if (file.size > MAX_FILE_SIZE) {
    return { valid: false, error: `File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB` };
  }
  
  if (!ALLOWED_MIME_TYPES.includes(file.type)) {
    return { valid: false, error: 'File must be an image (JPEG, PNG, WebP, or GIF)' };
  }
  
  return { valid: true };
}

// Get or create numeric user ID from database
async function getNumericUserId(supabase: Awaited<ReturnType<typeof createClient>>, userUuid: string): Promise<number | null> {
  try {
    const { data, error } = await supabase
      .rpc('get_or_create_numeric_user_id', { supabase_uuid: userUuid });
    
    if (error) {
      console.error('Error getting numeric user ID:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in getNumericUserId:', error);
    return null;
  }
}

export async function POST(request: NextRequest) {
  console.log("🎨 Web image generation request received");
  
  try {
    // Get authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log("❌ Authentication failed");
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log(`🔍 Authenticated user: ${user.id}`);

    // Parse request body
    const formData = await request.formData();
    const type = formData.get("type") as string; // "text" or "image"
    const prompt = formData.get("prompt") as string;
    const imageFile = formData.get("image") as File | null;

    console.log(`📝 Generation type: ${type}, prompt: "${prompt}"`);

    if (!type || (type !== "text" && type !== "image")) {
      return NextResponse.json(
        { success: false, error: "Invalid generation type. Must be 'text' or 'image'" },
        { status: 400 }
      );
    }

    if (type === "text" && !prompt) {
      return NextResponse.json(
        { success: false, error: "Prompt is required for text-to-image generation" },
        { status: 400 }
      );
    }

    if (type === "image" && !imageFile) {
      return NextResponse.json(
        { success: false, error: "Image file is required for image transformation" },
        { status: 400 }
      );
    }

    // Validate uploaded file if present
    if (imageFile) {
      const validation = validateFile(imageFile);
      if (!validation.valid) {
        return NextResponse.json(
          { success: false, error: validation.error },
          { status: 400 }
        );
      }
    }

    // Get or create numeric user ID from database (safe, collision-free)
    const numericUserId = await getNumericUserId(supabase, user.id);
    if (!numericUserId) {
      console.error(`❌ Failed to get numeric user ID for ${user.id}`);
      return NextResponse.json(
        { success: false, error: "Error processing user ID. Please try again." },
        { status: 500 }
      );
    }

    console.log(`🔍 Checking generation limits for user ${numericUserId} (${user.id})...`);

    // Check rate limits using existing function
    const generationCheck = await checkGenerationAllowed(numericUserId);
    if (!generationCheck) {
      console.error(`❌ Error checking limits for user ${numericUserId}`);
      return NextResponse.json(
        { success: false, error: "Error checking your limits. Please try again." },
        { status: 500 }
      );
    }

    if (!generationCheck.allowed) {
      let message = "";
      if (generationCheck.user_remaining <= 0) {
        message = `❌ You've reached your daily limit of ${generationCheck.user_limit} generations. Resets daily at midnight UTC.`;
      } else if (generationCheck.global_remaining <= 0) {
        message = `❌ Global daily limit reached. Try again tomorrow.`;
      } else {
        message = "❌ Generation not allowed at this time.";
      }
      
      console.log(`❌ Generation denied for user ${numericUserId}: ${message}`);
      return NextResponse.json(
        { 
          success: false, 
          error: message,
          limits: {
            user_remaining: generationCheck.user_remaining,
            global_remaining: generationCheck.global_remaining
          }
        },
        { status: 429 }
      );
    }

    console.log(`✅ Generation allowed for user ${numericUserId}. Starting image generation...`);

    let finalPrompt = "";
    
    if (type === "text") {
      // Enhanced prompt with CryBaby style (same as Telegram bot)
      finalPrompt = `Retro cartoon illustration. Thick black outlines, smooth flat shading, limited warm vintage palette (muted oranges, ochres, teal accents). 1950s newspaper comic style, rounded shapes, subtle paper-grain texture. Clean vector aesthetic, high-resolution, simple background with soft abstract swirls in tan. ${prompt}`;
    } else {
      // Image transformation prompt (same as Telegram bot)
      finalPrompt = "Transform this image into a retro cartoon illustration with thick black outlines, smooth flat shading, limited warm vintage palette (muted oranges, ochres, teal accents). 1950s newspaper comic style, rounded shapes, subtle paper-grain texture. Clean vector aesthetic, high-resolution, simple background with soft abstract swirls in tan.";
    }
    
    console.log(`🎨 Generating image with enhanced prompt for user ${numericUserId}...`);
    
    // Generate image using existing function
    const result = await generateImage(finalPrompt);

    if (result.success && result.imageUrl) {
      console.log(`✅ Image generated successfully for user ${numericUserId}: ${result.imageUrl}`);
      
      // Decrement both user and global limits atomically
      const decrementSuccess = await decrementBothLimits(numericUserId);
      
      if (!decrementSuccess) {
        console.error(`❌ Failed to decrement limits for user ${numericUserId}`);
        return NextResponse.json(
          { success: false, error: "Limits changed while processing. Please try again." },
          { status: 409 }
        );
      }
      
      console.log(`✅ Limits decremented for user ${numericUserId}`);
      
      // Log successful generation
      const logPrompt = type === "text" ? prompt : "Direct image transformation";
      await logGeneration(numericUserId, logPrompt, true, result.imageUrl);
      console.log(`📝 Generation logged for user ${numericUserId}`);

      return NextResponse.json({
        success: true,
        imageUrl: result.imageUrl,
        originalUrl: result.originalUrl,
        limits: {
          user_remaining: generationCheck.user_remaining - 1,
          global_remaining: generationCheck.global_remaining - 1
        },
        message: "🎨 Image generated successfully!"
      });
    } else {
      console.error(`❌ Image generation failed for user ${numericUserId}: ${result.error}`);
      
      // Log failed generation
      const logPrompt = type === "text" ? prompt : "Direct image transformation";
      await logGeneration(numericUserId, logPrompt, false, undefined, result.error);
      
      return NextResponse.json(
        { success: false, error: result.error || "Failed to generate image" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("🚨 API error:", error);
    
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
