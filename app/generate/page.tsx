import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { ImageGenerator } from "@/components/image-generator";

export default async function GeneratePage() {
  const supabase = await createClient();

  const { data, error } = await supabase.auth.getClaims();
  if (error || !data?.claims) {
    redirect("/auth/login");
  }

  return (
    <div className="min-h-screen bg-[#004643] py-8 px-4">
      <ImageGenerator />
    </div>
  );
}
