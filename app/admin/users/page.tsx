"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Edit, Save, X } from "lucide-react";

interface UserLimits {
  user_id: number;
  daily_limit: number;
  remaining: number;
  last_reset: string;
}

export default function UsersPage() {
  const [users, setUsers] = useState<UserLimits[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingUser, setEditingUser] = useState<number | null>(null);
  const [editLimit, setEditLimit] = useState("");

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await fetch("/api/admin/users");
      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (user: UserLimits) => {
    setEditingUser(user.user_id);
    setEditLimit(user.daily_limit.toString());
  };

  const handleSave = async (userId: number) => {
    try {
      const response = await fetch("/api/admin/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_id: userId,
          daily_limit: parseInt(editLimit),
        }),
      });

      if (response.ok) {
        await fetchUsers();
        setEditingUser(null);
        setEditLimit("");
      }
    } catch (error) {
      console.error("Error updating user:", error);
    }
  };

  const handleCancel = () => {
    setEditingUser(null);
    setEditLimit("");
  };

  if (loading) {
    return <div>Loading users...</div>;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">User Management</h1>
        <p className="text-muted-foreground">Manage user generation limits</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>User Limits</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User ID</TableHead>
                <TableHead>Daily Limit</TableHead>
                <TableHead>Remaining</TableHead>
                <TableHead>Last Reset</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.user_id}>
                  <TableCell className="font-medium">{user.user_id}</TableCell>
                  <TableCell>
                    {editingUser === user.user_id ? (
                      <Input
                        type="number"
                        value={editLimit}
                        onChange={(e) => setEditLimit(e.target.value)}
                        className="w-20"
                      />
                    ) : (
                      user.daily_limit
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge variant={user.remaining > 0 ? "default" : "destructive"}>
                      {user.remaining}
                    </Badge>
                  </TableCell>
                  <TableCell>{user.last_reset}</TableCell>
                  <TableCell>
                    {editingUser === user.user_id ? (
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleSave(user.user_id)}
                        >
                          <Save className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleCancel}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(user)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}