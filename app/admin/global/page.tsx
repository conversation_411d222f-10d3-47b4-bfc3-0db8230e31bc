"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Globe, Save, RotateCcw, TrendingUp, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface GlobalSettings {
  daily_limit: number;
  remaining: number;
  last_reset: string;
}

export default function GlobalPage() {
  const [settings, setSettings] = useState<GlobalSettings | null>(null);
  const [newLimit, setNewLimit] = useState("");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await fetch("/api/admin/global");
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
        setNewLimit(data.daily_limit.toString());
      } else {
        setError("Failed to fetch global settings");
      }
    } catch (error) {
      console.error("Error fetching settings:", error);
      setError("Error fetching global settings");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!newLimit || parseInt(newLimit) < 1) {
      setError("Daily limit must be at least 1");
      return;
    }

    setSaving(true);
    setError("");
    setSuccess("");

    try {
      const response = await fetch("/api/admin/global", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          daily_limit: parseInt(newLimit),
        }),
      });

      if (response.ok) {
        await fetchSettings();
        setSuccess("Global daily limit updated successfully!");
        setTimeout(() => setSuccess(""), 3000);
      } else {
        const data = await response.json();
        setError(data.error || "Failed to update global limit");
      }
    } catch (error) {
      console.error("Error updating settings:", error);
      setError("Error updating global limit");
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (settings) {
      setNewLimit(settings.daily_limit.toString());
      setError("");
      setSuccess("");
    }
  };

  const getUtilization = () => {
    if (!settings) return 0;
    return ((settings.daily_limit - settings.remaining) / settings.daily_limit) * 100;
  };

  const getStatusColor = () => {
    if (!settings) return "secondary";
    if (settings.remaining === 0) return "destructive";
    if (settings.remaining < settings.daily_limit * 0.2) return "default";
    return "outline";
  };

  const getStatusText = () => {
    if (!settings) return "Loading...";
    if (settings.remaining === 0) return "Depleted";
    if (settings.remaining < settings.daily_limit * 0.2) return "Low";
    return "Active";
  };

  if (loading) {
    return <div>Loading global settings...</div>;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Global Limits</h1>
        <p className="text-muted-foreground">Manage system-wide daily generation limits</p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Current Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Daily Limit</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{settings?.daily_limit}</div>
            <p className="text-xs text-muted-foreground">
              Total system capacity
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Remaining</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{settings?.remaining}</div>
            <p className="text-xs text-muted-foreground">
              {settings ? settings.daily_limit - settings.remaining : 0} used ({getUtilization().toFixed(1)}%)
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            <RotateCcw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant={getStatusColor()}>
                {getStatusText()}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              Last reset: {settings?.last_reset}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Global Limit Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="daily_limit">Daily Limit</Label>
              <Input
                id="daily_limit"
                type="number"
                value={newLimit}
                onChange={(e) => setNewLimit(e.target.value)}
                placeholder="Enter daily limit"
                min="1"
              />
              <p className="text-xs text-muted-foreground">
                Maximum number of images that can be generated system-wide per day
              </p>
            </div>

            <div className="space-y-2">
              <Label>Impact</Label>
              <div className="p-3 bg-muted rounded-md">
                <p className="text-sm">
                  Setting limit to <strong>{newLimit || "0"}</strong> will:
                </p>
                <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                  <li>• Reset remaining to {newLimit || "0"}</li>
                  <li>• Update last reset to today</li>
                  <li>• Affect all users immediately</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleSave}
              disabled={saving || !newLimit || parseInt(newLimit) < 1}
            >
              <Save className="h-4 w-4 mr-2" />
              {saving ? "Saving..." : "Save Changes"}
            </Button>
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={saving}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* System Info */}
      <Card>
        <CardHeader>
          <CardTitle>System Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-2">How Global Limits Work</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Applied system-wide across all users</li>
                <li>• Checked alongside individual user limits</li>
                <li>• Both limits must have capacity for generation</li>
                <li>• Automatically resets daily at midnight UTC</li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-2">Current Configuration</h4>
              <div className="text-sm text-muted-foreground space-y-1">
                <p>Daily Limit: <strong>{settings?.daily_limit}</strong></p>
                <p>Remaining: <strong>{settings?.remaining}</strong></p>
                <p>Used Today: <strong>{settings ? settings.daily_limit - settings.remaining : 0}</strong></p>
                <p>Utilization: <strong>{getUtilization().toFixed(1)}%</strong></p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}