# Image Generation Web App Implementation Plan

## Color Palette
- **Background:** `#004643`
- **Headline:** `#fffffe` 
- **Paragraph:** `#abd1c6`
- **Button:** `#f9bc60`
- **Button text:** `#001e1d`
- **Stroke:** `#001e1d`
- **Main:** `#e8e4e6`
- **Highlight:** `#f9bc60`
- **Secondary:** `#abd1c6`
- **Tertiary:** `#e16162`

## Implementation Checklist

### Phase 1: API Infrastructure
- [x] Create `/app/api/generate/route.ts` - Main API endpoint
- [x] Adapt rate limiting for Supabase auth UUIDs
- [ ] Test API with both text and image inputs

### Phase 2: UI Components
- [x] Add `components/ui/textarea.tsx`
- [x] Create `components/image-generator.tsx` - Main container
- [x] Create `components/text-to-image-form.tsx` - Text prompt form
- [x] Create `components/image-transform-form.tsx` - File upload form
- [x] Create `components/generated-image-display.tsx` - Result display
- [x] Create `components/rate-limit-display.tsx` - Limits indicator
- [x] Create `components/generation-history.tsx` - User history

### Phase 3: Main Generation Page
- [x] Create `/app/generate/page.tsx` - Protected image generation page
- [x] Implement tabbed interface (Text-to-Image vs Transform Image)
- [x] Add real-time rate limit display
- [x] Add generation history section
- [x] Ensure mobile responsiveness

### Phase 4: Navigation & Integration
- [x] Update navigation to include generate page
- [x] Update protected layout
- [x] Test authentication flow
- [x] Test complete user journey

### Phase 5: Styling & Polish
- [x] Apply custom color palette throughout
- [x] Optimize loading states and animations
- [x] Add proper error handling UI
- [x] Test on mobile devices

## Technical Notes
- Reuse existing OpenAI integration from `lib/bot/utils/openai.ts`
- Reuse existing watermarking from `lib/bot/utils/watermark.ts`
- Adapt rate limiting from `lib/bot/utils/supabase.ts` for web users
- Use FormData for file uploads
- Implement proper loading states (generation takes 10-30 seconds)
