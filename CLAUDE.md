# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start Commands

```bash
bun run dev          # Start development server with Turbopack
bun run build        # Build the application for production
bun run start        # Start production server
bun run lint         # Run ESLint
bun run webhook      # Set up Telegram webhook (requires .env.local)
bun run test-webhook # Test webhook functionality
```

## Project Architecture

This is a **Next.js 15 application** with integrated **Telegram bot** for AI-powered image generation. The system implements a hybrid rate limiting architecture with both global and per-user limits, comprehensive admin dashboard, and production-ready deployment pipeline.

### Tech Stack Core
- **Framework**: Next.js 15 (App Router) with React 19
- **Runtime**: Bun for package management and JavaScript execution
- **Database**: Supabase (PostgreSQL) with Row Level Security
- **Authentication**: Supabase Auth with SSR support
- **Bot Framework**: Grammy for Telegram bot operations
- **AI Integration**: OpenAI DALL-E 3 for image generation
- **Image Processing**: Sharp for watermarking and optimization
- **Styling**: Tailwind CSS + shadcn/ui components (New York style)
- **Development**: Turbopack for fast dev builds

### Key Architecture Patterns

**Hybrid Rate Limiting System**:
- Global daily limits (system-wide capacity control)
- Per-user daily limits (individual quotas)
- Both limits must have capacity for generation to proceed
- Atomic operations prevent race conditions
- Automatic daily resets at midnight UTC

**Database Schema** (see `database/complete_schema.sql`):
- `cry_global_settings`: System-wide daily limits
- `cry_user_limits`: Per-user daily quotas  
- `cry_generation_logs`: Comprehensive audit trail
- PostgreSQL functions for atomic operations and daily resets

**Supabase Client Pattern**:
- `lib/supabase/client.ts`: Browser client for client-side operations
- `lib/supabase/server.ts`: Server client for server-side operations
- `lib/supabase/middleware.ts`: Middleware client for session management

**Bot Integration**:
- `app/api/webhook/telegram/route.ts`: Webhook endpoint for Telegram updates
- `lib/bot/`: Bot utilities for watermarking, OpenAI integration, and logo processing
- Commands: `/start`, `/generate [prompt]`, `/limit`, `/help`
- Direct photo transformation support

**Authentication Flow**:
- Middleware redirects unauthenticated users to `/auth/login`
- Protected routes use server-side Supabase client
- Session persistence via HTTP-only cookies

### Application Structure

**Admin Dashboard** (`app/admin/`):
- `dashboard/`: Real-time analytics and system overview
- `global/`: Global rate limit management
- `users/`: User limit configuration and management
- `logs/`: Generation history with search and filtering

**API Endpoints** (`app/api/`):
- `admin/`: Admin panel API endpoints
- `webhook/telegram/`: Telegram bot webhook handler

**Components**:
- `components/ui/`: shadcn/ui components
- `components/tutorial/`: Onboarding tutorial components

### Environment Variables

**Required**:
```env
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=
SUPABASE_SERVICE_KEY=
TELEGRAM_BOT_TOKEN=
OPENAI_API_KEY=
WEBHOOK_URL=
```

**Optional Watermark Configuration**:
```env
WATERMARK_ENABLED=true
WATERMARK_POSITION=bottom-right
WATERMARK_OPACITY=0.8
WATERMARK_SCALE=0.1
```

### Database Operations

**Core Functions** (PostgreSQL):
- `check_generation_allowed(user_id)`: Validates both limit types
- `decrement_both_limits(user_id)`: Atomically decrements counters
- `check_and_reset_global_daily_limit()`: Handles global resets
- `check_and_reset_user_daily_limit(user_id)`: Handles user resets

**Management Patterns**:
- Always use provided functions for consistency
- Both global and user limits must have capacity
- Automatic daily resets handled by database functions
- Comprehensive monitoring queries available

### Bot Command Implementation

**Text Commands**:
- `/start`: Welcome message and usage instructions
- `/generate [prompt]`: AI image generation with CryBaby style
- `/limit`: Check remaining daily generations
- `/help`: Feature overview and usage guide

**Photo Processing**:
- Direct photo upload → CryBaby style transformation
- Automatic watermark application
- Prompt enhancement for consistent artistic style

### Deployment Pipeline

**Scripts** (`scripts/`):
- `setup-webhook.ts`: Configure Telegram webhook
- `test-webhook.ts`: Validate webhook functionality
- `verify-deployment.ts`: Post-deployment health checks
- `register-commands.ts`: Bot command registration

**Deployment Flow**:
1. Build application: `bun run build`
2. Deploy to Vercel
3. Configure webhook: `bun run webhook`
4. Verify deployment: `bun run verify-deployment`

### Security Implementation

**Row Level Security**:
- All tables protected with RLS policies
- Service role for bot operations
- Authenticated role for admin panel access

**API Security**:
- Telegram webhook authentication
- OpenAI API key protection
- Input validation and HTML escaping
- Rate limiting at multiple levels

### Performance Optimizations

**Database**:
- Indexes on user_id and timestamp columns
- Atomic operations for limit decrements
- Efficient queries for analytics dashboard

**Image Processing**:
- Sharp for fast watermarking
- Optimized OpenAI API calls
- Efficient storage in Supabase

**Frontend**:
- React Server Components by default
- Turbopack for fast development
- Optimized bundle sizes

### Development Notes

- Uses TypeScript strict mode
- Path aliases: `@/` points to project root
- Middleware handles authentication on all routes
- shadcn/ui components follow New York style variant
- Comprehensive error handling and user feedback

### Important Files

- `middleware.ts`: Authentication and route protection
- `database/complete_schema.sql`: Complete database schema with functions
- `app/api/webhook/telegram/route.ts`: Main bot logic
- `lib/bot/utils/`: Bot utilities (watermark, OpenAI, logo)
- `components.json`: shadcn/ui configuration
- `scripts/`: Deployment and testing utilities

### Testing and Validation

**Bot Testing**:
- Use `bun run test-webhook` to validate webhook
- Test rate limiting with multiple users
- Verify image generation pipeline

**Database Testing**:
- Use provided SQL queries in schema file
- Test limit enforcement and resets
- Validate RLS policies

**Deployment Testing**:
- Run `bun run verify-deployment` after deployment
- Check webhook configuration with Telegram
- Monitor logs for error patterns