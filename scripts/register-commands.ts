#!/usr/bin/env bun

/**
 * <PERSON><PERSON>t to register Telegram bot commands with the Bot API
 * This makes commands show up in the command menu when users type "/"
 */

import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;

if (!TELEGRAM_BOT_TOKEN) {
  console.error("❌ TELEGRAM_BOT_TOKEN is not set");
  process.exit(1);
}

const TELEGRAM_API_BASE = `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}`;

interface TelegramCommand {
  command: string;
  description: string;
}

// Define bot commands with descriptions
const commands: TelegramCommand[] = [
  {
    command: "start",
    description: "🎨 Welcome to the AI Image Generator Bot"
  },
  {
    command: "generate",
    description: "🖼️ Generate an image from your text description"
  },
  {
    command: "limit", 
    description: "📊 Check your remaining daily image generations"
  },
  {
    command: "help",
    description: "❓ Show help and usage information"
  }
];

async function registerCommands() {
  console.log("🤖 Registering Telegram bot commands...");
  
  try {
    const response = await fetch(`${TELEGRAM_API_BASE}/setMyCommands`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        commands: commands,
        scope: {
          type: "default" // Register for all users
        }
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${data.description || 'Unknown error'}`);
    }

    if (data.ok) {
      console.log("✅ Commands registered successfully!");
      console.log("📋 Registered commands:");
      commands.forEach(cmd => {
        console.log(`   /${cmd.command} - ${cmd.description}`);
      });
      
      console.log("\n🎉 Users can now see these commands in their Telegram command menu!");
    } else {
      console.error("❌ Failed to register commands:", data.description);
      process.exit(1);
    }
  } catch (error) {
    console.error("❌ Error registering commands:", error);
    process.exit(1);
  }
}

async function verifyCommands() {
  console.log("\n🔍 Verifying registered commands...");
  
  try {
    const response = await fetch(`${TELEGRAM_API_BASE}/getMyCommands`);
    const data = await response.json();

    if (data.ok && data.result) {
      console.log("✅ Current registered commands:");
      data.result.forEach((cmd: TelegramCommand) => {
        console.log(`   /${cmd.command} - ${cmd.description}`);
      });
    } else {
      console.error("❌ Could not verify commands:", data.description);
    }
  } catch (error) {
    console.error("❌ Error verifying commands:", error);
  }
}

// Main execution
async function main() {
  console.log("🚀 Telegram Bot Command Registration");
  console.log("=====================================");
  
  await registerCommands();
  await verifyCommands();
  
  console.log("\n✨ Command registration complete!");
  console.log("💡 Tip: Users may need to restart their Telegram app to see the new commands");
}

main().catch(console.error);