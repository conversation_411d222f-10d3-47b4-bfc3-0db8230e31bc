#!/usr/bin/env bun

/**
 * <PERSON><PERSON><PERSON> to set up Telegram webhook for the bot
 * This script configures the Telegram bot to use webhooks instead of polling
 */

import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;
const WEBHOOK_URL = process.env.WEBHOOK_URL;

if (!TELEGRAM_BOT_TOKEN) {
  console.error("❌ TELEGRAM_BOT_TOKEN is not set in .env.local");
  process.exit(1);
}

if (!WEBHOOK_URL) {
  console.error("❌ WEBHOOK_URL is not set in .env.local");
  process.exit(1);
}

const TELEGRAM_API_BASE = `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}`;

async function setWebhook() {
  console.log("🔧 Setting up Telegram webhook...");
  console.log(`📡 Webhook URL: ${WEBHOOK_URL}`);
  
  try {
    // First, delete any existing webhook
    console.log("🗑️  Deleting existing webhook...");
    const deleteResponse = await fetch(`${TELEGRAM_API_BASE}/deleteWebhook`, {
      method: "POST",
    });
    
    const deleteResult = await deleteResponse.json();
    console.log("🗑️  Delete webhook result:", deleteResult);
    
    // Set the new webhook
    console.log("📡 Setting new webhook...");
    const setResponse = await fetch(`${TELEGRAM_API_BASE}/setWebhook`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        url: WEBHOOK_URL,
        allowed_updates: ["message", "callback_query"],
        drop_pending_updates: true,
      }),
    });
    
    const setResult = await setResponse.json();
    
    if (setResult.ok) {
      console.log("✅ Webhook set successfully!");
      console.log("📋 Result:", setResult);
    } else {
      console.error("❌ Failed to set webhook:");
      console.error(setResult);
      process.exit(1);
    }
    
    // Verify webhook info
    console.log("🔍 Verifying webhook info...");
    const infoResponse = await fetch(`${TELEGRAM_API_BASE}/getWebhookInfo`);
    const infoResult = await infoResponse.json();
    
    if (infoResult.ok) {
      console.log("📊 Webhook info:");
      console.log(`   URL: ${infoResult.result.url}`);
      console.log(`   Has custom certificate: ${infoResult.result.has_custom_certificate}`);
      console.log(`   Pending update count: ${infoResult.result.pending_update_count}`);
      console.log(`   Last error date: ${infoResult.result.last_error_date || "None"}`);
      console.log(`   Last error message: ${infoResult.result.last_error_message || "None"}`);
      console.log(`   Max connections: ${infoResult.result.max_connections}`);
      console.log(`   Allowed updates: ${JSON.stringify(infoResult.result.allowed_updates)}`);
    } else {
      console.error("❌ Failed to get webhook info:");
      console.error(infoResult);
    }
    
  } catch (error) {
    console.error("❌ Error setting up webhook:", error);
    process.exit(1);
  }
}

async function getMe() {
  console.log("🤖 Getting bot info...");
  
  try {
    const response = await fetch(`${TELEGRAM_API_BASE}/getMe`);
    const result = await response.json();
    
    if (result.ok) {
      console.log("✅ Bot info:");
      console.log(`   ID: ${result.result.id}`);
      console.log(`   Username: @${result.result.username}`);
      console.log(`   First name: ${result.result.first_name}`);
      console.log(`   Can join groups: ${result.result.can_join_groups}`);
      console.log(`   Can read all group messages: ${result.result.can_read_all_group_messages}`);
      console.log(`   Supports inline queries: ${result.result.supports_inline_queries}`);
    } else {
      console.error("❌ Failed to get bot info:");
      console.error(result);
    }
  } catch (error) {
    console.error("❌ Error getting bot info:", error);
  }
}

async function main() {
  console.log("🚀 Starting Telegram webhook setup...");
  console.log("=" .repeat(50));
  
  await getMe();
  console.log("-".repeat(50));
  await setWebhook();
  
  console.log("=" .repeat(50));
  console.log("✅ Webhook setup completed!");
  console.log("");
  console.log("🔗 Your bot is now configured to receive updates via webhook:");
  console.log(`   ${WEBHOOK_URL}`);
  console.log("");
  console.log("📝 Next steps:");
  console.log("   1. Deploy your application to Vercel");
  console.log("   2. Test the webhook by sending a message to your bot");
  console.log("   3. Check the logs in your deployment dashboard");
  console.log("");
  console.log("🧪 To test locally:");
  console.log("   1. Use ngrok or similar to expose your local server");
  console.log("   2. Update WEBHOOK_URL in .env.local");
  console.log("   3. Run this script again");
}

// Run the main function
main().catch(console.error);
