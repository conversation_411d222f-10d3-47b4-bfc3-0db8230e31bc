#!/usr/bin/env bun

/**
 * <PERSON><PERSON>t to verify the deployment and webhook configuration
 */

import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;
const WEBHOOK_URL = process.env.WEBHOOK_URL;

if (!TELEGRAM_BOT_TOKEN) {
  console.error("❌ TELEGRAM_BOT_TOKEN is not set");
  process.exit(1);
}

if (!WEBHOOK_URL) {
  console.error("❌ WEBHOOK_URL is not set");
  process.exit(1);
}

const TELEGRAM_API_BASE = `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}`;

async function checkWebhookInfo() {
  console.log("🔍 Checking current webhook configuration...");
  
  try {
    const response = await fetch(`${TELEGRAM_API_BASE}/getWebhookInfo`);
    const result = await response.json();
    
    if (result.ok) {
      const info = result.result;
      console.log("📊 Current webhook status:");
      console.log(`   ✅ URL: ${info.url}`);
      console.log(`   📊 Pending updates: ${info.pending_update_count}`);
      console.log(`   🔗 Max connections: ${info.max_connections}`);
      console.log(`   📝 Allowed updates: ${JSON.stringify(info.allowed_updates)}`);
      
      if (info.last_error_date) {
        console.log(`   ⚠️  Last error: ${new Date(info.last_error_date * 1000).toISOString()}`);
        console.log(`   📄 Error message: ${info.last_error_message}`);
      } else {
        console.log("   ✅ No recent errors");
      }
      
      if (info.url === WEBHOOK_URL) {
        console.log("✅ Webhook URL matches configuration");
      } else {
        console.log("⚠️  Webhook URL mismatch:");
        console.log(`   Expected: ${WEBHOOK_URL}`);
        console.log(`   Actual: ${info.url}`);
      }
      
      return true;
    } else {
      console.error("❌ Failed to get webhook info:", result);
      return false;
    }
  } catch (error) {
    console.error("❌ Error checking webhook info:", error);
    return false;
  }
}

async function checkBotInfo() {
  console.log("🤖 Checking bot information...");
  
  try {
    const response = await fetch(`${TELEGRAM_API_BASE}/getMe`);
    const result = await response.json();
    
    if (result.ok) {
      const bot = result.result;
      console.log("✅ Bot information:");
      console.log(`   🆔 ID: ${bot.id}`);
      console.log(`   👤 Username: @${bot.username}`);
      console.log(`   📛 Name: ${bot.first_name}`);
      console.log(`   👥 Can join groups: ${bot.can_join_groups}`);
      console.log(`   📖 Can read all messages: ${bot.can_read_all_group_messages}`);
      return true;
    } else {
      console.error("❌ Failed to get bot info:", result);
      return false;
    }
  } catch (error) {
    console.error("❌ Error checking bot info:", error);
    return false;
  }
}

async function checkEndpointHealth() {
  console.log("🏥 Checking webhook endpoint health...");
  
  if (!WEBHOOK_URL) {
    console.error("❌ WEBHOOK_URL is not set");
    return false;
  }
  
  try {
    const response = await fetch(WEBHOOK_URL, {
      method: "GET",
    });
    
    console.log(`📊 Response status: ${response.status}`);
    
    if (response.ok) {
      try {
        const result = await response.json();
        console.log("✅ Endpoint is healthy:");
        console.log(`   📄 Response: ${JSON.stringify(result, null, 2)}`);
        return true;
      } catch {
        console.log("✅ Endpoint is reachable (non-JSON response)");
        return true;
      }
    } else {
      console.log("⚠️  Endpoint returned non-200 status (this may be normal for POST-only endpoints)");
      return true; // This is actually expected for a POST-only webhook
    }
  } catch (error) {
    console.error("❌ Error checking endpoint health:", error);
    return false;
  }
}

async function main() {
  console.log("🚀 Starting deployment verification...");
  console.log("=" .repeat(60));
  
  const checks = [
    { name: "Bot Information", fn: checkBotInfo },
    { name: "Webhook Configuration", fn: checkWebhookInfo },
    { name: "Endpoint Health", fn: checkEndpointHealth },
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    console.log(`\n🔍 ${check.name}:`);
    console.log("-".repeat(40));
    
    const passed = await check.fn();
    if (!passed) {
      allPassed = false;
    }
  }
  
  console.log("\n" + "=" .repeat(60));
  
  if (allPassed) {
    console.log("🎉 All checks passed! Your bot webhook is ready.");
    console.log("");
    console.log("📱 To test your bot:");
    console.log(`   1. Open Telegram and search for @${process.env.TELEGRAM_BOT_TOKEN?.split(':')[0] || 'your_bot'}`);
    console.log("   2. Send /start to begin");
    console.log("   3. Try /generate a cute cat to test image generation");
    console.log("");
    console.log("📊 Monitor your bot:");
    console.log("   • Check Vercel function logs for webhook activity");
    console.log("   • Use /limit command to check rate limits");
    console.log("   • Admin panel: https://crybaby-one.vercel.app/admin");
  } else {
    console.log("❌ Some checks failed. Please review the errors above.");
    console.log("");
    console.log("🔧 Common fixes:");
    console.log("   • Ensure your Vercel deployment is successful");
    console.log("   • Check that environment variables are set correctly");
    console.log("   • Verify the webhook URL is accessible");
    console.log("   • Run 'bun run setup-webhook' to reconfigure");
  }
}

// Run the main function
main().catch(console.error);
