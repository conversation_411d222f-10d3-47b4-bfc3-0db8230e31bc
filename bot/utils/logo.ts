import sharp from 'sharp';
import path from 'path';

export async function createLogo(): Promise<Buffer> {
  // Create a simple logo with text on a semi-transparent background
  const logoText = 'CryBaby';
  const logoSize = 200;
  
  // Create SVG logo
  const svgLogo = `
    <svg width="${logoSize}" height="60" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
        </linearGradient>
        <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
        </filter>
      </defs>
      
      <!-- Background with rounded corners -->
      <rect x="5" y="5" width="${logoSize - 10}" height="50" rx="10" ry="10" 
            fill="rgba(0,0,0,0.7)" filter="url(#shadow)"/>
      
      <!-- Main text -->
      <text x="${logoSize / 2}" y="35" text-anchor="middle" 
            font-family="Arial, sans-serif" font-size="20" font-weight="bold" 
            fill="url(#grad1)">
        ${logoText}
      </text>
      
      <!-- Subtitle -->
      <text x="${logoSize / 2}" y="50" text-anchor="middle" 
            font-family="Arial, sans-serif" font-size="10" 
            fill="rgba(255,255,255,0.8)">
        AI Image Bot
      </text>
    </svg>
  `;
  
  // Convert SVG to PNG buffer
  const logoBuffer = await sharp(Buffer.from(svgLogo))
    .png()
    .toBuffer();
    
  return logoBuffer;
}

export async function getLogoBuffer(): Promise<Buffer> {
  try {
    // Try to read existing logo file first
    const logoPath = path.join(__dirname, '..', 'assets', 'logo.png');
    try {
      return await sharp(logoPath).png().toBuffer();
    } catch {
      // If no logo file exists, create one programmatically
      const logo = await createLogo();
      
      // Save it for future use
      await sharp(logo).png().toFile(logoPath);
      
      return logo;
    }
  } catch (error) {
    console.error('Error creating logo:', error);
    throw error;
  }
}