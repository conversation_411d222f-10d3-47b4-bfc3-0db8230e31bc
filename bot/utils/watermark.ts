import sharp from 'sharp';
import { getLogoBuffer } from './logo';

export interface WatermarkOptions {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center';
  opacity?: number;
  padding?: number;
  scale?: number;
}

export async function addWatermark(
  imageBuffer: Buffer,
  options: WatermarkOptions = {}
): Promise<Buffer> {
  const {
    position = 'bottom-right',
    opacity = 0.8,
    padding = 20,
    scale = 0.15
  } = options;

  try {
    // Get the main image metadata
    const mainImage = sharp(imageBuffer);
    const { width, height } = await mainImage.metadata();
    
    if (!width || !height) {
      throw new Error('Unable to get image dimensions');
    }

    // Get logo buffer
    const logoBuffer = await getLogoBuffer();
    
    // Calculate logo dimensions based on main image size
    const logoWidth = Math.floor(width * scale);
    const logoHeight = Math.floor(logoWidth * 0.3); // Maintain aspect ratio
    
    // Resize logo and adjust opacity
    const resizedLogo = await sharp(logoBuffer)
      .resize(logoWidth, logoHeight, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .png()
      .toBuffer();

    // Calculate position based on the chosen position
    let left: number;
    let top: number;

    switch (position) {
      case 'bottom-right':
        left = width - logoWidth - padding;
        top = height - logoHeight - padding;
        break;
      case 'bottom-left':
        left = padding;
        top = height - logoHeight - padding;
        break;
      case 'top-right':
        left = width - logoWidth - padding;
        top = padding;
        break;
      case 'top-left':
        left = padding;
        top = padding;
        break;
      case 'center':
        left = Math.floor((width - logoWidth) / 2);
        top = Math.floor((height - logoHeight) / 2);
        break;
      default:
        left = width - logoWidth - padding;
        top = height - logoHeight - padding;
    }

    // Create composite image with watermark
    const watermarkedImage = await mainImage
      .composite([
        {
          input: resizedLogo,
          left: Math.max(0, left),
          top: Math.max(0, top),
          blend: 'over'
        }
      ])
      .png()
      .toBuffer();

    return watermarkedImage;
  } catch (error) {
    console.error('Error adding watermark:', error);
    throw error;
  }
}

export function getWatermarkConfig(): WatermarkOptions {
  return {
    position: (process.env.WATERMARK_POSITION as WatermarkOptions['position']) || 'bottom-right',
    opacity: process.env.WATERMARK_OPACITY ? parseFloat(process.env.WATERMARK_OPACITY) : 0.8,
    padding: process.env.WATERMARK_PADDING ? parseInt(process.env.WATERMARK_PADDING) : 20,
    scale: process.env.WATERMARK_SCALE ? parseFloat(process.env.WATERMARK_SCALE) : 0.12
  };
}

export function isWatermarkEnabled(): boolean {
  return process.env.WATERMARK_ENABLED !== 'false';
}

export async function addWatermarkFromUrl(
  imageUrl: string,
  options: WatermarkOptions = {}
): Promise<Buffer> {
  try {
    // Check if watermarking is enabled
    if (!isWatermarkEnabled()) {
      // If disabled, just download and return the original image
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.statusText}`);
      }
      return Buffer.from(await response.arrayBuffer());
    }

    // Download the image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.statusText}`);
    }
    
    const imageBuffer = Buffer.from(await response.arrayBuffer());
    
    // Merge with environment config
    const finalOptions = { ...getWatermarkConfig(), ...options };
    
    // Add watermark
    return await addWatermark(imageBuffer, finalOptions);
  } catch (error) {
    console.error('Error downloading and watermarking image:', error);
    throw error;
  }
}