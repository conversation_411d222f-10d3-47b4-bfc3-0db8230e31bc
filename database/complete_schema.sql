-- =====================================================
-- COMPLETE HYBRID RATE LIMITING SYSTEM FOR TELEGRAM BOT
-- =====================================================
-- This file contains the complete database schema, functions, policies,
-- and management queries for the hybrid rate limiting system.
-- 
-- The system implements BOTH:
-- 1. Global daily limits (system-wide capacity)
-- 2. Per-user daily limits (individual quotas)
--
-- Both limits must have available capacity for generation to proceed.
-- =====================================================

-- =====================================================
-- SECTION 1: TABLE DEFINITIONS
-- =====================================================

-- Global settings table (shared pool for all users)
CREATE TABLE cry_global_settings (
    id INTEGER PRIMARY KEY DEFAULT 1,
    daily_limit INTEGER NOT NULL DEFAULT 100,
    remaining INTEGER NOT NULL DEFAULT 100,
    last_reset DATE NOT NULL DEFAULT CURRENT_DATE,
    CONSTRAINT single_row CHECK (id = 1)
);

-- User limits table (individual user quotas)
CREATE TABLE cry_user_limits (
    user_id BIGINT PRIMARY KEY,
    daily_limit INTEGER NOT NULL DEFAULT 10,
    remaining INTEGER NOT NULL DEFAULT 10,
    last_reset DATE NOT NULL DEFAULT CURRENT_DATE
);

-- Generation logs table (audit trail)
CREATE TABLE cry_generation_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id BIGINT NOT NULL,
    prompt TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    success BOOLEAN NOT NULL,
    image_url TEXT,
    error_message TEXT
);

-- =====================================================
-- SECTION 2: INITIAL DATA AND INDEXES
-- =====================================================

-- Insert default global settings
INSERT INTO cry_global_settings (id, daily_limit, remaining, last_reset) 
VALUES (1, 100, 100, CURRENT_DATE)
ON CONFLICT (id) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cry_user_limits_user_id ON cry_user_limits(user_id);
CREATE INDEX IF NOT EXISTS idx_cry_generation_logs_user_id ON cry_generation_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_cry_generation_logs_timestamp ON cry_generation_logs(timestamp);

-- =====================================================
-- SECTION 3: ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable Row Level Security
ALTER TABLE cry_global_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE cry_user_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE cry_generation_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for cry_global_settings
CREATE POLICY "Service role can manage global settings" ON cry_global_settings
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admin users can read global settings" ON cry_global_settings
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admin users can update global settings" ON cry_global_settings
    FOR UPDATE USING (auth.role() = 'authenticated');

-- RLS Policies for cry_user_limits
CREATE POLICY "Service role can manage user limits" ON cry_user_limits
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admin users can read user limits" ON cry_user_limits
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admin users can update user limits" ON cry_user_limits
    FOR UPDATE USING (auth.role() = 'authenticated');

-- RLS Policies for cry_generation_logs
CREATE POLICY "Service role can manage generation logs" ON cry_generation_logs
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admin users can read generation logs" ON cry_generation_logs
    FOR SELECT USING (auth.role() = 'authenticated');

-- =====================================================
-- SECTION 4: CORE FUNCTIONS
-- =====================================================

-- Function to check and reset global daily limit
CREATE OR REPLACE FUNCTION check_and_reset_global_daily_limit()
RETURNS cry_global_settings AS $$
DECLARE
    settings_record cry_global_settings;
    current_date_val DATE := CURRENT_DATE;
BEGIN
    SELECT * INTO settings_record FROM cry_global_settings WHERE id = 1;
    
    IF settings_record.last_reset < current_date_val THEN
        -- Reset daily limit
        UPDATE cry_global_settings 
        SET remaining = daily_limit, last_reset = current_date_val
        WHERE id = 1
        RETURNING * INTO settings_record;
    END IF;
    
    RETURN settings_record;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check and reset user daily limit
CREATE OR REPLACE FUNCTION check_and_reset_user_daily_limit(user_id_param BIGINT)
RETURNS cry_user_limits AS $$
DECLARE
    user_record cry_user_limits;
    current_date_val DATE := CURRENT_DATE;
BEGIN
    SELECT * INTO user_record FROM cry_user_limits WHERE user_id = user_id_param;
    
    IF NOT FOUND THEN
        -- Create new user with default limits
        INSERT INTO cry_user_limits (user_id, last_reset) VALUES (user_id_param, current_date_val)
        RETURNING * INTO user_record;
    ELSIF user_record.last_reset < current_date_val THEN
        -- Reset daily limit
        UPDATE cry_user_limits 
        SET remaining = daily_limit, last_reset = current_date_val
        WHERE user_id = user_id_param
        RETURNING * INTO user_record;
    END IF;
    
    RETURN user_record;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if generation is allowed (checks BOTH limits)
CREATE OR REPLACE FUNCTION check_generation_allowed(user_id_param BIGINT)
RETURNS JSON AS $$
DECLARE
    user_record cry_user_limits;
    global_record cry_global_settings;
    result JSON;
BEGIN
    -- Check and reset user limit
    SELECT * INTO user_record FROM check_and_reset_user_daily_limit(user_id_param);
    
    -- Check and reset global limit
    SELECT * INTO global_record FROM check_and_reset_global_daily_limit();
    
    -- Build result JSON
    result := json_build_object(
        'allowed', (user_record.remaining > 0 AND global_record.remaining > 0),
        'user_remaining', user_record.remaining,
        'user_limit', user_record.daily_limit,
        'global_remaining', global_record.remaining,
        'global_limit', global_record.daily_limit,
        'reason', CASE 
            WHEN user_record.remaining <= 0 THEN 'user_limit_exceeded'
            WHEN global_record.remaining <= 0 THEN 'global_limit_exceeded'
            ELSE 'allowed'
        END
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrement both limits atomically
CREATE OR REPLACE FUNCTION decrement_both_limits(user_id_param BIGINT)
RETURNS BOOLEAN AS $$
DECLARE
    user_remaining INTEGER;
    global_remaining INTEGER;
BEGIN
    -- Get current remaining amounts
    SELECT remaining INTO user_remaining FROM cry_user_limits WHERE user_id = user_id_param;
    SELECT remaining INTO global_remaining FROM cry_global_settings WHERE id = 1;
    
    -- Check if both have capacity
    IF user_remaining <= 0 OR global_remaining <= 0 THEN
        RETURN FALSE;
    END IF;
    
    -- Decrement both limits atomically
    UPDATE cry_user_limits 
    SET remaining = remaining - 1 
    WHERE user_id = user_id_param;
    
    UPDATE cry_global_settings 
    SET remaining = remaining - 1 
    WHERE id = 1;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- SECTION 5: MANAGEMENT QUERIES
-- =====================================================

-- ===== GLOBAL LIMIT MANAGEMENT =====

-- Get current global settings
SELECT 
    daily_limit,
    remaining,
    last_reset,
    CASE 
        WHEN remaining = 0 THEN 'DEPLETED'
        WHEN remaining < daily_limit * 0.2 THEN 'LOW'
        ELSE 'ACTIVE'
    END as status,
    ROUND(((daily_limit - remaining) * 100.0 / daily_limit), 2) as utilization_percent
FROM cry_global_settings 
WHERE id = 1;

-- Set global daily limit (resets remaining to new limit)
UPDATE cry_global_settings 
SET 
    daily_limit = 100,  -- Change this value
    remaining = 100,    -- Change this value
    last_reset = CURRENT_DATE
WHERE id = 1;

-- Reset global limit to full capacity
UPDATE cry_global_settings 
SET 
    remaining = daily_limit,
    last_reset = CURRENT_DATE
WHERE id = 1;

-- ===== USER LIMIT MANAGEMENT =====

-- Get all users with their limits
SELECT 
    user_id,
    daily_limit,
    remaining,
    last_reset,
    CASE 
        WHEN remaining = 0 THEN 'DEPLETED'
        WHEN remaining < daily_limit * 0.2 THEN 'LOW'
        ELSE 'ACTIVE'
    END as status
FROM cry_user_limits 
ORDER BY user_id;

-- Get specific user's limits
SELECT 
    user_id,
    daily_limit,
    remaining,
    last_reset,
    CASE WHEN remaining > 0 THEN true ELSE false END as can_generate
FROM cry_user_limits 
WHERE user_id = 123456789;  -- Replace with actual user ID

-- Set user daily limit
UPDATE cry_user_limits 
SET 
    daily_limit = 10,  -- Change this value
    remaining = 10     -- Change this value
WHERE user_id = 123456789;  -- Replace with actual user ID

-- Reset specific user's limit to full capacity
UPDATE cry_user_limits 
SET 
    remaining = daily_limit,
    last_reset = CURRENT_DATE
WHERE user_id = 123456789;  -- Replace with actual user ID

-- Reset ALL users' limits to full capacity
UPDATE cry_user_limits 
SET 
    remaining = daily_limit,
    last_reset = CURRENT_DATE;

-- Add new user with default limit
INSERT INTO cry_user_limits (user_id, daily_limit, remaining, last_reset)
VALUES (123456789, 5, 5, CURRENT_DATE)  -- Replace with actual user ID
ON CONFLICT (user_id) DO NOTHING;

-- ===== BULK OPERATIONS =====

-- Set all users to same daily limit
UPDATE cry_user_limits 
SET 
    daily_limit = 5,  -- Change this value
    remaining = 5;    -- Change this value

-- Reset everyone and everything (nuclear option)
UPDATE cry_global_settings 
SET 
    remaining = daily_limit,
    last_reset = CURRENT_DATE
WHERE id = 1;

UPDATE cry_user_limits 
SET 
    remaining = daily_limit,
    last_reset = CURRENT_DATE;

-- ===== MONITORING AND ANALYTICS =====

-- Overall system health check
SELECT 
    'Global' as limit_type,
    NULL as user_id,
    g.daily_limit,
    g.remaining,
    g.last_reset,
    CASE 
        WHEN g.remaining = 0 THEN 'DEPLETED'
        WHEN g.remaining < g.daily_limit * 0.2 THEN 'LOW'
        ELSE 'ACTIVE'
    END as status
FROM cry_global_settings g
WHERE g.id = 1

UNION ALL

SELECT 
    'User' as limit_type,
    u.user_id,
    u.daily_limit,
    u.remaining,
    u.last_reset,
    CASE 
        WHEN u.remaining = 0 THEN 'DEPLETED'
        WHEN u.remaining < u.daily_limit * 0.2 THEN 'LOW'
        ELSE 'ACTIVE'
    END as status
FROM cry_user_limits u
ORDER BY limit_type, user_id;

-- Users who have exhausted their limits
SELECT 
    user_id,
    daily_limit,
    remaining,
    last_reset
FROM cry_user_limits 
WHERE remaining = 0
ORDER BY user_id;

-- Users with low remaining capacity (< 20%)
SELECT 
    user_id,
    daily_limit,
    remaining,
    last_reset,
    ROUND((remaining * 100.0 / daily_limit), 2) as remaining_percent
FROM cry_user_limits 
WHERE remaining < daily_limit * 0.2 AND remaining > 0
ORDER BY remaining_percent;

-- Generation statistics by user (today only)
SELECT 
    user_id,
    COUNT(*) as total_attempts,
    COUNT(CASE WHEN success = true THEN 1 END) as successful_generations,
    COUNT(CASE WHEN success = false THEN 1 END) as failed_generations,
    ROUND(
        (COUNT(CASE WHEN success = true THEN 1 END) * 100.0 / COUNT(*)), 2
    ) as success_rate_percent
FROM cry_generation_logs 
WHERE timestamp >= CURRENT_DATE
GROUP BY user_id
ORDER BY total_attempts DESC;

-- Daily usage trends (last 7 days)
SELECT 
    DATE(timestamp) as date,
    COUNT(*) as total_attempts,
    COUNT(CASE WHEN success = true THEN 1 END) as successful_generations,
    COUNT(DISTINCT user_id) as unique_users
FROM cry_generation_logs 
WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(timestamp)
ORDER BY date DESC;

-- System capacity utilization
SELECT 
    g.daily_limit as global_limit,
    g.remaining as global_remaining,
    (g.daily_limit - g.remaining) as global_used,
    ROUND(((g.daily_limit - g.remaining) * 100.0 / g.daily_limit), 2) as global_utilization_percent,
    COUNT(u.user_id) as total_users,
    COUNT(CASE WHEN u.remaining = 0 THEN 1 END) as depleted_users,
    COUNT(CASE WHEN u.remaining > 0 THEN 1 END) as active_users
FROM cry_global_settings g
CROSS JOIN cry_user_limits u
WHERE g.id = 1
GROUP BY g.daily_limit, g.remaining;

-- ===== MAINTENANCE OPERATIONS =====

-- Force daily reset (run at midnight UTC)
UPDATE cry_global_settings 
SET 
    remaining = daily_limit,
    last_reset = CURRENT_DATE
WHERE id = 1;

UPDATE cry_user_limits 
SET 
    remaining = daily_limit,
    last_reset = CURRENT_DATE
WHERE last_reset < CURRENT_DATE;

-- Clean up old logs (older than 30 days)
DELETE FROM cry_generation_logs 
WHERE timestamp < CURRENT_DATE - INTERVAL '30 days';

-- ===== EMERGENCY OPERATIONS =====

-- Emergency: Disable all generation (set limits to 0)
UPDATE cry_global_settings 
SET remaining = 0 
WHERE id = 1;

-- Emergency: Enable all generation (set to max capacity)
UPDATE cry_global_settings 
SET remaining = daily_limit 
WHERE id = 1;

UPDATE cry_user_limits 
SET remaining = daily_limit;

-- ===== TESTING AND VALIDATION =====

-- Test generation check for specific user
SELECT check_generation_allowed(123456789);  -- Replace with actual user ID

-- Verify hybrid system integrity
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM cry_global_settings WHERE id = 1) THEN 'OK'
        ELSE 'MISSING'
    END as global_settings_status,
    CASE 
        WHEN COUNT(*) > 0 THEN 'OK'
        ELSE 'EMPTY'
    END as user_limits_status,
    COUNT(*) as total_users
FROM cry_user_limits;

-- ===== REPORTING QUERIES =====

-- Daily report
SELECT 
    CURRENT_DATE as report_date,
    g.daily_limit as global_capacity,
    g.remaining as global_remaining,
    (g.daily_limit - g.remaining) as global_used,
    COUNT(u.user_id) as total_users,
    COUNT(CASE WHEN u.remaining > 0 THEN 1 END) as users_with_capacity,
    COUNT(CASE WHEN u.remaining = 0 THEN 1 END) as users_depleted,
    COALESCE(l.total_attempts, 0) as total_attempts_today,
    COALESCE(l.successful_generations, 0) as successful_generations_today
FROM cry_global_settings g
CROSS JOIN cry_user_limits u
LEFT JOIN (
    SELECT 
        COUNT(*) as total_attempts,
        COUNT(CASE WHEN success = true THEN 1 END) as successful_generations
    FROM cry_generation_logs 
    WHERE timestamp >= CURRENT_DATE
) l ON true
WHERE g.id = 1
GROUP BY g.daily_limit, g.remaining, l.total_attempts, l.successful_generations;

-- Usage by hour (today)
SELECT 
    EXTRACT(HOUR FROM timestamp) as hour,
    COUNT(*) as attempts,
    COUNT(CASE WHEN success = true THEN 1 END) as successful
FROM cry_generation_logs 
WHERE timestamp >= CURRENT_DATE
GROUP BY EXTRACT(HOUR FROM timestamp)
ORDER BY hour;

-- =====================================================
-- SECTION 6: IMPORTANT NOTES FOR ADMINISTRATORS
-- =====================================================

/*
HYBRID RATE LIMITING SYSTEM - IMPLEMENTATION NOTES:

1. ARCHITECTURE:
   - Dual-layer rate limiting: Global (system-wide) + Per-user limits
   - Both limits must have capacity for generation to proceed
   - Atomic operations prevent race conditions

2. KEY FUNCTIONS:
   - check_generation_allowed(): Main function to check both limits
   - decrement_both_limits(): Atomically decrements both counters
   - Auto-reset functions handle daily resets transparently

3. DAILY OPERATIONS:
   - Limits automatically reset at midnight UTC via functions
   - Manual reset queries provided for emergency use
   - Monitor global utilization to adjust capacity

4. BEST PRACTICES:
   - Always use provided functions for consistency
   - Test limit changes in non-production first
   - Monitor both global and user utilization rates
   - Keep logs for at least 30 days for analysis

5. SECURITY:
   - RLS policies restrict access appropriately
   - Service role required for bot operations
   - Authenticated users can manage via admin panel

6. PERFORMANCE:
   - Indexes on user_id and timestamp for fast queries
   - Atomic operations ensure data integrity
   - Single-row constraint on global settings

7. TROUBLESHOOTING:
   - Check both limit types when debugging
   - Use integrity check queries to validate system
   - Review logs for failed generation attempts

8. SCALING CONSIDERATIONS:
   - Global limit controls total system load
   - User limits ensure fair distribution
   - Adjust based on infrastructure capacity

Remember: This is a fail-safe system. If either limit is exceeded,
generation is blocked to protect system resources.
*/

-- =====================================================
-- END OF COMPLETE SCHEMA
-- =====================================================