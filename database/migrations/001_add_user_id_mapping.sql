-- =====================================================
-- USER ID MAPPING TABLE MIGRATION
-- =====================================================
-- This migration adds a user ID mapping table to safely convert
-- Supabase UUIDs to numeric IDs for the existing rate limiting system.

-- Create user ID mapping table
CREATE TABLE cry_user_id_mappings (
    id SERIAL PRIMARY KEY,
    supabase_uuid UUID NOT NULL UNIQUE,
    numeric_id BIGINT NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_cry_user_id_mappings_supabase_uuid ON cry_user_id_mappings(supabase_uuid);
CREATE INDEX IF NOT EXISTS idx_cry_user_id_mappings_numeric_id ON cry_user_id_mappings(numeric_id);

-- Enable Row Level Security
ALTER TABLE cry_user_id_mappings ENABLE ROW LEVEL SECURITY;

-- RLS Policies for cry_user_id_mappings
CREATE POLICY "Service role can manage user ID mappings" ON cry_user_id_mappings
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Admin users can read user ID mappings" ON cry_user_id_mappings
    FOR SELECT USING (auth.role() = 'authenticated');

-- Function to get or create numeric ID for a Supabase UUID
CREATE OR REPLACE FUNCTION get_or_create_numeric_user_id(supabase_uuid UUID)
RETURNS BIGINT AS $$
DECLARE
    existing_mapping RECORD;
    new_numeric_id BIGINT;
BEGIN
    -- Check if mapping already exists
    SELECT numeric_id INTO existing_mapping
    FROM cry_user_id_mappings
    WHERE supabase_uuid = $1;
    
    IF FOUND THEN
        RETURN existing_mapping.numeric_id;
    END IF;
    
    -- Generate new numeric ID using extract from UUID
    -- This ensures uniqueness and consistency
    new_numeric_id := abs(('x' || substr(replace(supabase_uuid::text, '-', ''), 1, 15))::bit(60)::bigint);
    
    -- Handle potential collisions by incrementing
    WHILE EXISTS (SELECT 1 FROM cry_user_id_mappings WHERE numeric_id = new_numeric_id) LOOP
        new_numeric_id := new_numeric_id + 1;
    END LOOP;
    
    -- Insert new mapping
    INSERT INTO cry_user_id_mappings (supabase_uuid, numeric_id)
    VALUES ($1, new_numeric_id)
    ON CONFLICT (supabase_uuid) DO UPDATE SET numeric_id = EXCLUDED.numeric_id;
    
    RETURN new_numeric_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to service role
GRANT EXECUTE ON FUNCTION get_or_create_numeric_user_id(UUID) TO service_role;