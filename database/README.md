# Database Setup

## Setting up the Supabase Database

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Run the `schema.sql` file to create the necessary tables and functions

## Tables Created

### `cry_user_limits`
- `user_id` (BIGINT, Primary Key): Telegram user ID
- `daily_limit` (INTEGER): Max generations per day (default: 5)
- `remaining` (INTEGER): Remaining generations today
- `last_reset` (DATE): Date of last reset

### `cry_generation_logs`
- `id` (UUID, Primary Key): Unique log entry ID
- `user_id` (BIGINT): Telegram user ID
- `prompt` (TEXT): User's image generation prompt
- `timestamp` (TIMESTAMP): When the request was made
- `success` (BOOLEAN): Whether generation was successful
- `image_url` (TEXT): URL of generated image (if successful)
- `error_message` (TEXT): Error message (if failed)

## Functions Created

### `ensure_user_limits(user_id_param BIGINT)`
Ensures a user exists in the cry_user_limits table, creating a new record if needed.

### `check_and_reset_daily_limit(user_id_param BIGINT)`
Checks if daily limits need to be reset and resets them if necessary.

## Row Level Security (RLS)

RLS is enabled on both tables with policies that:
- Allow service role (bot) to manage all data
- Allow authenticated admin users to read and update data
- Prevent unauthorized access to user data

## Usage

The bot will use the service key to interact with these tables, while the admin panel will use the authenticated user context.