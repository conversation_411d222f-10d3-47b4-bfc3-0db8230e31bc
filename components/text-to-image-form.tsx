"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Wand2, <PERSON>rk<PERSON> } from "lucide-react";

interface TextToImageFormProps {
  onGenerate: (prompt: string) => Promise<void>;
  isGenerating: boolean;
  disabled?: boolean;
}

export function TextToImageForm({ onGenerate, isGenerating, disabled }: TextToImageFormProps) {
  const [prompt, setPrompt] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || isGenerating || disabled) return;
    
    await onGenerate(prompt.trim());
  };

  const examplePrompts = [
    "a cat wearing sunglasses in a retro diner",
    "a sunset over mountains with vintage vibes",
    "a futuristic city with warm orange tones",
    "a cute robot reading a book",
    "a cozy coffee shop on a rainy day"
  ];

  const handleExampleClick = (example: string) => {
    setPrompt(example);
  };

  return (
    <Card className="bg-[#004643] border-[#abd1c6]/20">
      <CardHeader>
        <CardTitle className="text-[#fffffe] flex items-center gap-2">
          <Wand2 className="h-5 w-5 text-[#f9bc60]" />
          Text to Image
        </CardTitle>
        <p className="text-[#abd1c6] text-sm">
          Describe what you want to create and we&apos;ll generate it in CryBaby&apos;s signature retro cartoon style.
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="prompt" className="text-[#fffffe]">
              Describe your image
            </Label>
            <Textarea
              id="prompt"
              placeholder="e.g., a cat wearing sunglasses in a retro diner..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="min-h-[100px] bg-[#001e1d] border-[#abd1c6]/30 text-[#fffffe] placeholder:text-[#abd1c6]/60 focus:border-[#f9bc60] focus:ring-[#f9bc60]"
              disabled={isGenerating || disabled}
            />
            <p className="text-xs text-[#abd1c6]/80">
              Your prompt will be enhanced with CryBaby&apos;s signature retro cartoon style automatically.
            </p>
          </div>

          <Button
            type="submit"
            disabled={!prompt.trim() || isGenerating || disabled}
            className="w-full bg-[#f9bc60] hover:bg-[#f9bc60]/90 text-[#001e1d] font-medium"
          >
            {isGenerating ? (
              <>
                <Sparkles className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Wand2 className="mr-2 h-4 w-4" />
                Generate Image
              </>
            )}
          </Button>
        </form>

        {/* Example prompts */}
        <div className="space-y-2">
          <Label className="text-[#fffffe] text-xs">Quick examples:</Label>
          <div className="flex flex-wrap gap-2">
            {examplePrompts.map((example, index) => (
              <button
                key={index}
                onClick={() => handleExampleClick(example)}
                disabled={isGenerating || disabled}
                className="text-xs px-2 py-1 bg-[#abd1c6]/10 hover:bg-[#abd1c6]/20 text-[#abd1c6] rounded-md border border-[#abd1c6]/20 hover:border-[#abd1c6]/40 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {example}
              </button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
