"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, Zap } from "lucide-react";

interface RateLimitDisplayProps {
  userRemaining: number;
  globalRemaining: number;
  userLimit?: number;
  globalLimit?: number;
  hoursUntilReset?: number;
}

export function RateLimitDisplay({ 
  userRemaining, 
  globalRemaining, 
  userLimit = 10,
  globalLimit = 1000,
  hoursUntilReset = 24 
}: RateLimitDisplayProps) {
  const userPercentage = (userRemaining / userLimit) * 100;
  const globalPercentage = (globalRemaining / globalLimit) * 100;
  
  const getUserLimitColor = () => {
    if (userPercentage > 50) return "bg-[#abd1c6]";
    if (userPercentage > 20) return "bg-[#f9bc60]";
    return "bg-[#e16162]";
  };

  const getGlobalLimitColor = () => {
    if (globalPercentage > 50) return "bg-[#abd1c6]";
    if (globalPercentage > 20) return "bg-[#f9bc60]";
    return "bg-[#e16162]";
  };

  return (
    <Card className="bg-[#004643] border-[#abd1c6]/20">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-[#fffffe] flex items-center gap-2">
            <Zap className="h-4 w-4 text-[#f9bc60]" />
            Generation Limits
          </h3>
          <div className="flex items-center gap-1 text-xs text-[#abd1c6]">
            <Clock className="h-3 w-3" />
            Resets in {hoursUntilReset}h
          </div>
        </div>
        
        <div className="space-y-3">
          {/* User Limit */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-[#abd1c6]">Your Daily Limit</span>
              <Badge 
                variant="outline" 
                className={`${getUserLimitColor()} text-[#001e1d] border-none text-xs`}
              >
                {userRemaining}/{userLimit}
              </Badge>
            </div>
            <div className="w-full bg-[#001e1d] rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${getUserLimitColor()}`}
                style={{ width: `${userPercentage}%` }}
              />
            </div>
          </div>

          {/* Global Limit */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-[#abd1c6]">Global Daily Limit</span>
              <Badge 
                variant="outline" 
                className={`${getGlobalLimitColor()} text-[#001e1d] border-none text-xs`}
              >
                {globalRemaining}/{globalLimit}
              </Badge>
            </div>
            <div className="w-full bg-[#001e1d] rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${getGlobalLimitColor()}`}
                style={{ width: `${globalPercentage}%` }}
              />
            </div>
          </div>
        </div>

        {(userRemaining === 0 || globalRemaining === 0) && (
          <div className="mt-3 p-2 bg-[#e16162]/10 border border-[#e16162]/20 rounded-md">
            <p className="text-xs text-[#e16162]">
              {userRemaining === 0 
                ? "You've reached your daily limit. Try again tomorrow!" 
                : "Global daily limit reached. Try again later!"}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
