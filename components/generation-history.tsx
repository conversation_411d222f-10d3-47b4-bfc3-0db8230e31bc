"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { History, Download, Eye, Calendar } from "lucide-react";

interface GenerationHistoryItem {
  id: string;
  prompt: string;
  imageUrl: string;
  timestamp: string;
  success: boolean;
}

export function GenerationHistory() {
  const [history, setHistory] = useState<GenerationHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        const response = await fetch('/api/history?limit=50');
        const data = await response.json();
        
        if (data.success) {
          setHistory(data.history);
        } else {
          console.error('Failed to fetch history:', data.error);
          setHistory([]);
        }
      } catch (error) {
        console.error('Error fetching generation history:', error);
        setHistory([]);
      } finally {
        setLoading(false);
      }
    };

    fetchHistory();
  }, []);

  const handleDownload = async (imageUrl: string, prompt: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `crybaby-${prompt.slice(0, 20).replace(/[^a-zA-Z0-9]/g, '-')}-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }
  };

  const displayedHistory = expanded ? history : history.slice(0, 3);

  if (loading) {
    return (
      <Card className="bg-[#004643] border-[#abd1c6]/20">
        <CardHeader>
          <CardTitle className="text-[#fffffe] flex items-center gap-2">
            <History className="h-5 w-5 text-[#f9bc60]" />
            Generation History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="flex gap-4">
                  <div className="w-16 h-16 bg-[#abd1c6]/20 rounded-lg"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-[#abd1c6]/20 rounded w-3/4"></div>
                    <div className="h-3 bg-[#abd1c6]/20 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (history.length === 0) {
    return (
      <Card className="bg-[#004643] border-[#abd1c6]/20">
        <CardHeader>
          <CardTitle className="text-[#fffffe] flex items-center gap-2">
            <History className="h-5 w-5 text-[#f9bc60]" />
            Generation History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <History className="h-12 w-12 text-[#abd1c6]/40 mx-auto mb-4" />
            <p className="text-[#abd1c6]">No generations yet</p>
            <p className="text-[#abd1c6]/60 text-sm">Your generated images will appear here</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-[#004643] border-[#abd1c6]/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-[#fffffe] flex items-center gap-2">
            <History className="h-5 w-5 text-[#f9bc60]" />
            Generation History
          </CardTitle>
          <Badge className="bg-[#abd1c6]/20 text-[#abd1c6] border-[#abd1c6]/30">
            {history.length} total
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {displayedHistory.map((item) => (
          <div key={item.id} className="flex gap-4 p-3 bg-[#001e1d]/50 rounded-lg border border-[#abd1c6]/10">
            <div className="relative">
              <img
                src={item.imageUrl}
                alt={item.prompt}
                className="w-16 h-16 object-cover rounded-lg border border-[#abd1c6]/20"
              />
              <Badge 
                className={`absolute -top-1 -right-1 text-xs ${
                  item.success 
                    ? 'bg-[#abd1c6] text-[#001e1d]' 
                    : 'bg-[#e16162] text-[#fffffe]'
                }`}
              >
                {item.success ? '✓' : '✗'}
              </Badge>
            </div>
            
            <div className="flex-1 min-w-0">
              <p className="text-[#fffffe] text-sm font-medium truncate">
                {item.prompt}
              </p>
              <div className="flex items-center gap-2 mt-1">
                <Calendar className="h-3 w-3 text-[#abd1c6]/60" />
                <span className="text-[#abd1c6]/60 text-xs">
                  {formatTimeAgo(item.timestamp)}
                </span>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => window.open(item.imageUrl, '_blank')}
                className="border-[#abd1c6]/30 text-[#abd1c6] hover:bg-[#abd1c6]/10 hover:text-[#fffffe] h-8 w-8 p-0"
              >
                <Eye className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleDownload(item.imageUrl, item.prompt)}
                className="border-[#abd1c6]/30 text-[#abd1c6] hover:bg-[#abd1c6]/10 hover:text-[#fffffe] h-8 w-8 p-0"
              >
                <Download className="h-3 w-3" />
              </Button>
            </div>
          </div>
        ))}
        
        {history.length > 3 && (
          <Button
            variant="outline"
            onClick={() => setExpanded(!expanded)}
            className="w-full border-[#abd1c6]/30 text-[#abd1c6] hover:bg-[#abd1c6]/10 hover:text-[#fffffe]"
          >
            {expanded ? 'Show Less' : `Show All (${history.length - 3} more)`}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
