"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Download, Share2, <PERSON>, Co<PERSON>, Check } from "lucide-react";

interface GeneratedImageDisplayProps {
  imageUrl: string;
  originalUrl?: string;
  prompt?: string;
  timestamp?: Date;
  onDownload?: () => void;
}

export function GeneratedImageDisplay({ 
  imageUrl, 
  originalUrl, 
  prompt, 
  timestamp,
  onDownload 
}: GeneratedImageDisplayProps) {
  const [copied, setCopied] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleDownload = async () => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `crybaby-generated-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      onDownload?.();
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(imageUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Copy failed:', error);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'CryBaby Generated Image',
          text: prompt ? `Check out this image I generated: "${prompt}"` : 'Check out this CryBaby generated image!',
          url: imageUrl,
        });
      } catch (error) {
        console.error('Share failed:', error);
      }
    } else {
      // Fallback to copy link
      handleCopyLink();
    }
  };

  return (
    <Card className="bg-[#004643] border-[#abd1c6]/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-[#fffffe] flex items-center gap-2">
            <Eye className="h-5 w-5 text-[#f9bc60]" />
            Generated Image
          </CardTitle>
          <Badge className="bg-[#abd1c6]/20 text-[#abd1c6] border-[#abd1c6]/30">
            CryBaby Style
          </Badge>
        </div>
        {prompt && (
          <p className="text-[#abd1c6] text-sm">
            &quot;{prompt}&quot;
          </p>
        )}
        {timestamp && (
          <p className="text-[#abd1c6]/60 text-xs">
            Generated {timestamp.toLocaleString()}
          </p>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="relative">
          <img
            src={imageUrl}
            alt={prompt || "Generated image"}
            className={`w-full rounded-lg border border-[#abd1c6]/20 transition-opacity duration-300 ${
              imageLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            onLoad={() => setImageLoaded(true)}
          />
          {!imageLoaded && (
            <div className="absolute inset-0 bg-[#001e1d] rounded-lg flex items-center justify-center">
              <div className="animate-pulse text-[#abd1c6]">Loading image...</div>
            </div>
          )}
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            onClick={handleDownload}
            className="flex-1 bg-[#f9bc60] hover:bg-[#f9bc60]/90 text-[#001e1d] font-medium"
          >
            <Download className="mr-2 h-4 w-4" />
            Download
          </Button>
          
          <Button
            onClick={handleShare}
            variant="outline"
            className="flex-1 border-[#abd1c6]/30 text-[#abd1c6] hover:bg-[#abd1c6]/10 hover:text-[#fffffe]"
          >
            <Share2 className="mr-2 h-4 w-4" />
            Share
          </Button>
          
          <Button
            onClick={handleCopyLink}
            variant="outline"
            className="border-[#abd1c6]/30 text-[#abd1c6] hover:bg-[#abd1c6]/10 hover:text-[#fffffe]"
          >
            {copied ? (
              <Check className="h-4 w-4 text-[#abd1c6]" />
            ) : (
              <Copy className="h-4 w-4" />
            )}
          </Button>
        </div>

        <div className="bg-[#abd1c6]/10 border border-[#abd1c6]/20 rounded-md p-3">
          <p className="text-xs text-[#abd1c6]">
            ✨ This image has been watermarked with the CryBaby logo and enhanced with our signature retro cartoon style.
          </p>
        </div>

        {originalUrl && originalUrl !== imageUrl && (
          <details className="text-xs">
            <summary className="text-[#abd1c6] cursor-pointer hover:text-[#fffffe]">
              View original (before watermark)
            </summary>
            <div className="mt-2">
              <img
                src={originalUrl}
                alt="Original without watermark"
                className="w-full rounded border border-[#abd1c6]/20"
              />
            </div>
          </details>
        )}
      </CardContent>
    </Card>
  );
}
