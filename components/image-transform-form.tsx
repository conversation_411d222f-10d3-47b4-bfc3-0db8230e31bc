"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Upload, Image as ImageIcon, X, Palette } from "lucide-react";

interface ImageTransformFormProps {
  onTransform: (file: File) => Promise<void>;
  isGenerating: boolean;
  disabled?: boolean;
}

export function ImageTransformForm({ onTransform, isGenerating, disabled }: ImageTransformFormProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file (PNG, JPG, WEBP)');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB');
      return;
    }

    setSelectedFile(file);
    
    // Create preview URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedFile || isGenerating || disabled) return;
    
    await onTransform(selectedFile);
  };

  return (
    <Card className="bg-[#004643] border-[#abd1c6]/20">
      <CardHeader>
        <CardTitle className="text-[#fffffe] flex items-center gap-2">
          <Palette className="h-5 w-5 text-[#f9bc60]" />
          Transform Image
        </CardTitle>
        <p className="text-[#abd1c6] text-sm">
          Upload any photo and we&apos;ll transform it into CryBaby&apos;s signature retro cartoon style.
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          {!selectedFile ? (
            <div
              className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                dragActive 
                  ? 'border-[#f9bc60] bg-[#f9bc60]/5' 
                  : 'border-[#abd1c6]/30 hover:border-[#abd1c6]/50'
              } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
              onClick={() => !disabled && fileInputRef.current?.click()}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileInputChange}
                className="hidden"
                disabled={disabled}
              />
              
              <Upload className="mx-auto h-12 w-12 text-[#abd1c6]/60 mb-4" />
              <p className="text-[#fffffe] font-medium mb-2">
                Drop your image here or click to browse
              </p>
              <p className="text-[#abd1c6] text-sm">
                Supports PNG, JPG, WEBP (max 10MB)
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="relative">
                <img
                  src={previewUrl!}
                  alt="Preview"
                  className="w-full h-48 object-cover rounded-lg border border-[#abd1c6]/20"
                />
                <button
                  type="button"
                  onClick={handleRemoveFile}
                  disabled={isGenerating || disabled}
                  className="absolute top-2 right-2 p-1 bg-[#e16162] hover:bg-[#e16162]/90 text-white rounded-full transition-colors disabled:opacity-50"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              
              <div className="text-center">
                <p className="text-[#fffffe] font-medium">{selectedFile.name}</p>
                <p className="text-[#abd1c6] text-sm">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            </div>
          )}

          <Button
            type="submit"
            disabled={!selectedFile || isGenerating || disabled}
            className="w-full bg-[#f9bc60] hover:bg-[#f9bc60]/90 text-[#001e1d] font-medium"
          >
            {isGenerating ? (
              <>
                <Palette className="mr-2 h-4 w-4 animate-pulse" />
                Transforming...
              </>
            ) : (
              <>
                <ImageIcon className="mr-2 h-4 w-4" />
                Transform to CryBaby Style
              </>
            )}
          </Button>
        </form>

        <div className="bg-[#abd1c6]/10 border border-[#abd1c6]/20 rounded-md p-3">
          <p className="text-xs text-[#abd1c6]">
            <strong>Tip:</strong> Works best with clear photos of people, objects, or landscapes. 
            The AI will automatically apply CryBaby&apos;s retro cartoon aesthetic with thick outlines and vintage colors.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
