"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { RateLimitDisplay } from "@/components/rate-limit-display";
import { TextToImageForm } from "@/components/text-to-image-form";
import { ImageTransformForm } from "@/components/image-transform-form";
import { GeneratedImageDisplay } from "@/components/generated-image-display";
import { GenerationHistory } from "@/components/generation-history";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertCircle, Sparkles } from "lucide-react";

interface GenerationResult {
  success: boolean;
  imageUrl?: string;
  originalUrl?: string;
  error?: string;
  limits?: {
    user_remaining: number;
    global_remaining: number;
  };
  message?: string;
}

interface RateLimits {
  user_remaining: number;
  global_remaining: number;
  user_limit: number;
  global_limit: number;
  hours_until_reset: number;
}

export function ImageGenerator() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationResult, setGenerationResult] = useState<GenerationResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [rateLimits, setRateLimits] = useState<RateLimits | null>(null);
  const [activeTab, setActiveTab] = useState("text");

  // Fetch initial rate limits
  useEffect(() => {
    fetchRateLimits();
  }, []);

  const fetchRateLimits = async () => {
    try {
      // We'll make a test call to get current limits
      // This is a simple way to get limits without a separate endpoint
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'test' }) // This will fail but return limit info
      });
      
      if (response.status === 429) {
        const data = await response.json();
        if (data.limits) {
          setRateLimits({
            user_remaining: data.limits.user_remaining,
            global_remaining: data.limits.global_remaining,
            user_limit: 10, // Default values
            global_limit: 1000,
            hours_until_reset: data.limits.hours_until_reset || 24
          });
        }
      }
    } catch (error) {
      console.error('Failed to fetch rate limits:', error);
    }
  };

  const handleGenerate = async (prompt: string) => {
    setIsGenerating(true);
    setError(null);
    setGenerationResult(null);

    try {
      const formData = new FormData();
      formData.append('type', 'text');
      formData.append('prompt', prompt);

      const response = await fetch('/api/generate', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (data.success) {
        setGenerationResult(data);
        // Update rate limits
        if (data.limits) {
          setRateLimits(prev => prev ? {
            ...prev,
            user_remaining: data.limits.user_remaining,
            global_remaining: data.limits.global_remaining
          } : null);
        }
      } else {
        setError(data.error || 'Failed to generate image');
        // Update rate limits if provided (for rate limit errors)
        if (data.limits) {
          setRateLimits(prev => prev ? {
            ...prev,
            user_remaining: data.limits.user_remaining,
            global_remaining: data.limits.global_remaining,
            hours_until_reset: data.limits.hours_until_reset || prev.hours_until_reset
          } : null);
        }
      }
    } catch (error) {
      console.error('Generation error:', error);
      setError('Network error. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleTransform = async (file: File) => {
    setIsGenerating(true);
    setError(null);
    setGenerationResult(null);

    try {
      const formData = new FormData();
      formData.append('type', 'image');
      formData.append('image', file);

      const response = await fetch('/api/generate', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (data.success) {
        setGenerationResult(data);
        // Update rate limits
        if (data.limits) {
          setRateLimits(prev => prev ? {
            ...prev,
            user_remaining: data.limits.user_remaining,
            global_remaining: data.limits.global_remaining
          } : null);
        }
      } else {
        setError(data.error || 'Failed to transform image');
        // Update rate limits if provided (for rate limit errors)
        if (data.limits) {
          setRateLimits(prev => prev ? {
            ...prev,
            user_remaining: data.limits.user_remaining,
            global_remaining: data.limits.global_remaining,
            hours_until_reset: data.limits.hours_until_reset || prev.hours_until_reset
          } : null);
        }
      }
    } catch (error) {
      console.error('Transform error:', error);
      setError('Network error. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const isDisabled = rateLimits ? (rateLimits.user_remaining <= 0 || rateLimits.global_remaining <= 0) : false;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-[#fffffe] flex items-center justify-center gap-2">
          <Sparkles className="h-8 w-8 text-[#f9bc60]" />
          CryBaby Image Generator
        </h1>
        <p className="text-[#abd1c6]">
          Create stunning retro cartoon illustrations with AI
        </p>
      </div>

      {/* Rate Limits */}
      {rateLimits && (
        <RateLimitDisplay
          userRemaining={rateLimits.user_remaining}
          globalRemaining={rateLimits.global_remaining}
          userLimit={rateLimits.user_limit}
          globalLimit={rateLimits.global_limit}
          hoursUntilReset={rateLimits.hours_until_reset}
        />
      )}

      {/* Error Display */}
      {error && (
        <Alert className="bg-[#e16162]/10 border-[#e16162]/20">
          <AlertCircle className="h-4 w-4 text-[#e16162]" />
          <AlertDescription className="text-[#e16162]">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Generation Forms */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-[#001e1d] border border-[#abd1c6]/20">
          <TabsTrigger 
            value="text" 
            className="data-[state=active]:bg-[#f9bc60] data-[state=active]:text-[#001e1d] text-[#abd1c6]"
          >
            Text to Image
          </TabsTrigger>
          <TabsTrigger 
            value="transform" 
            className="data-[state=active]:bg-[#f9bc60] data-[state=active]:text-[#001e1d] text-[#abd1c6]"
          >
            Transform Image
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="text" className="space-y-4">
          <TextToImageForm
            onGenerate={handleGenerate}
            isGenerating={isGenerating}
            disabled={isDisabled}
          />
        </TabsContent>
        
        <TabsContent value="transform" className="space-y-4">
          <ImageTransformForm
            onTransform={handleTransform}
            isGenerating={isGenerating}
            disabled={isDisabled}
          />
        </TabsContent>
      </Tabs>

      {/* Generation Result */}
      {generationResult && generationResult.imageUrl && (
        <GeneratedImageDisplay
          imageUrl={generationResult.imageUrl}
          originalUrl={generationResult.originalUrl}
          timestamp={new Date()}
        />
      )}

      {/* Loading State */}
      {isGenerating && (
        <Card className="bg-[#004643] border-[#abd1c6]/20">
          <CardContent className="p-8 text-center">
            <div className="space-y-4">
              <Sparkles className="h-12 w-12 text-[#f9bc60] mx-auto animate-pulse" />
              <div>
                <h3 className="text-[#fffffe] font-medium">Creating your masterpiece...</h3>
                <p className="text-[#abd1c6] text-sm">This usually takes 10-30 seconds</p>
              </div>
              <div className="w-full bg-[#001e1d] rounded-full h-2">
                <div className="bg-[#f9bc60] h-2 rounded-full animate-pulse" style={{ width: '60%' }} />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Generation History */}
      <GenerationHistory />
    </div>
  );
}
